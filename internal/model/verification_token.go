package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
)

type VerificationTokenModel struct {
	ID        uuid.UUID  `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	UserID    uuid.UUID  `gorm:"type:uuid;not null;index" json:"user_id"`
	TokenHash string     `gorm:"not null;size:255;index" json:"-"`
	TokenType string     `gorm:"type:verification_type;not null;index" json:"token_type"`
	Email     string     `gorm:"not null;size:255;index" json:"email"`
	ExpiresAt time.Time  `gorm:"not null;index" json:"expires_at"`
	UsedAt    *time.Time `gorm:"index" json:"used_at"`
	CreatedAt time.Time  `gorm:"autoCreateTime" json:"created_at"`

	User UserModel `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"user,omitempty"`
}

func (VerificationTokenModel) TableName() string {
	return "verification_tokens"
}

func (v *VerificationTokenModel) ToDomain() *domain.VerificationToken {
	return &domain.VerificationToken{
		ID:        v.ID,
		UserID:    v.UserID,
		TokenHash: v.TokenHash,
		Type:      domain.VerificationType(v.TokenType),
		Email:     v.Email,
		ExpiresAt: v.ExpiresAt,
		UsedAt:    v.UsedAt,
		CreatedAt: v.CreatedAt,
	}
}

func (v *VerificationTokenModel) FromDomain(domainToken *domain.VerificationToken) {
	v.ID = domainToken.ID
	v.UserID = domainToken.UserID
	v.TokenHash = domainToken.TokenHash
	v.TokenType = string(domainToken.Type)
	v.Email = domainToken.Email
	v.ExpiresAt = domainToken.ExpiresAt
	v.UsedAt = domainToken.UsedAt
	v.CreatedAt = domainToken.CreatedAt
}

func (v *VerificationTokenModel) IsValid() bool {
	return v.UsedAt == nil && time.Now().Before(v.ExpiresAt)
}

func (v *VerificationTokenModel) IsExpired() bool {
	return time.Now().After(v.ExpiresAt)
}

func (v *VerificationTokenModel) MarkAsUsed() error {
	if !v.IsValid() {
		return domain.NewTokenUsedError()
	}
	now := time.Now()
	v.UsedAt = &now
	return nil
}

func (v *VerificationTokenModel) TimeUntilExpiry() time.Duration {
	if v.IsExpired() {
		return 0
	}
	return time.Until(v.ExpiresAt)
}
