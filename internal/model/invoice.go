package model

import (
	"time"

	"github.com/google/uuid"

	"github.com/smooth-inc/backend/internal/domain"
)

type InvoiceModel struct {
	ID               uuid.UUID  `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	StripeInvoiceID  string     `gorm:"uniqueIndex;not null" json:"stripe_invoice_id"`
	StripeCustomerID string     `gorm:"index;not null" json:"stripe_customer_id"`
	UserID           *uuid.UUID `gorm:"type:uuid;index" json:"user_id"`
	Amount           int        `gorm:"not null" json:"amount"`
	Currency         string     `gorm:"default:JPY" json:"currency"`
	Status           string     `gorm:"type:invoice_status;not null" json:"status"`
	Description      *string    `json:"description"`
	HostedInvoiceURL *string    `json:"hosted_invoice_url"`
	InvoicePDF       *string    `json:"invoice_pdf"`
	PaymentIntentID  *string    `gorm:"index" json:"payment_intent_id"`
	SubscriptionID   *string    `gorm:"index" json:"subscription_id"`
	AttemptCount     int        `gorm:"default:0" json:"attempt_count"`
	Attempted        bool       `gorm:"default:false" json:"attempted"`
	AutoAdvance      bool       `gorm:"default:true" json:"auto_advance"`
	BillingReason    *string    `json:"billing_reason"`
	DueDate          *time.Time `json:"due_date"`
	PaidAt           *time.Time `json:"paid_at"`
	CreatedAt        time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt        time.Time  `gorm:"autoUpdateTime" json:"updated_at"`

	User *UserModel `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
}

func (i *InvoiceModel) TableName() string {
	return "invoices"
}

func (i *InvoiceModel) ToDomain() *domain.Invoice {
	return &domain.Invoice{
		ID:               i.ID,
		StripeInvoiceID:  i.StripeInvoiceID,
		StripeCustomerID: i.StripeCustomerID,
		UserID:           i.UserID,
		Amount:           i.Amount,
		Currency:         i.Currency,
		Status:           domain.InvoiceStatus(i.Status),
		Description:      i.Description,
		HostedInvoiceURL: i.HostedInvoiceURL,
		InvoicePDF:       i.InvoicePDF,
		PaymentIntentID:  i.PaymentIntentID,
		SubscriptionID:   i.SubscriptionID,
		AttemptCount:     i.AttemptCount,
		Attempted:        i.Attempted,
		AutoAdvance:      i.AutoAdvance,
		BillingReason:    i.BillingReason,
		DueDate:          i.DueDate,
		PaidAt:           i.PaidAt,
		CreatedAt:        i.CreatedAt,
		UpdatedAt:        i.UpdatedAt,
	}
}

func (i *InvoiceModel) FromDomain(domainInvoice *domain.Invoice) {
	i.ID = domainInvoice.ID
	i.StripeInvoiceID = domainInvoice.StripeInvoiceID
	i.StripeCustomerID = domainInvoice.StripeCustomerID
	i.UserID = domainInvoice.UserID
	i.Amount = domainInvoice.Amount
	i.Currency = domainInvoice.Currency
	i.Status = string(domainInvoice.Status)
	i.Description = domainInvoice.Description
	i.HostedInvoiceURL = domainInvoice.HostedInvoiceURL
	i.InvoicePDF = domainInvoice.InvoicePDF
	i.PaymentIntentID = domainInvoice.PaymentIntentID
	i.SubscriptionID = domainInvoice.SubscriptionID
	i.AttemptCount = domainInvoice.AttemptCount
	i.Attempted = domainInvoice.Attempted
	i.AutoAdvance = domainInvoice.AutoAdvance
	i.BillingReason = domainInvoice.BillingReason
	i.DueDate = domainInvoice.DueDate
	i.PaidAt = domainInvoice.PaidAt
	i.CreatedAt = domainInvoice.CreatedAt
	i.UpdatedAt = domainInvoice.UpdatedAt
}
