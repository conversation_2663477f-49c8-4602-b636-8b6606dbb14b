package model

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"github.com/google/uuid"

	"github.com/smooth-inc/backend/internal/domain"
)

type DisputeModel struct {
	ID                    uuid.UUID         `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	StripeDisputeID       string            `gorm:"uniqueIndex;not null" json:"stripe_dispute_id"`
	StripeChargeID        string            `gorm:"index;not null" json:"stripe_charge_id"`
	PaymentID             *uuid.UUID        `gorm:"type:uuid;index" json:"payment_id"`
	Amount                int               `gorm:"not null" json:"amount"`
	Currency              string            `gorm:"default:JPY" json:"currency"`
	Reason                string            `gorm:"type:dispute_reason;not null" json:"reason"`
	Status                string            `gorm:"type:dispute_status;not null" json:"status"`
	EvidenceDueBy         *time.Time        `json:"evidence_due_by"`
	IsChargeRefundable    bool              `gorm:"default:false" json:"is_charge_refundable"`
	LiveMode              bool              `gorm:"default:false" json:"live_mode"`
	Metadata              MetadataJSON      `gorm:"type:jsonb" json:"metadata"`
	NetworkReasonCode     *string           `json:"network_reason_code"`
	CreatedAt             time.Time         `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt             time.Time         `gorm:"autoUpdateTime" json:"updated_at"`

	Payment *PaymentModel `gorm:"foreignKey:PaymentID;references:ID" json:"payment,omitempty"`
}

type MetadataJSON map[string]string

func (m MetadataJSON) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	return json.Marshal(m)
}

func (m *MetadataJSON) Scan(value interface{}) error {
	if value == nil {
		*m = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, m)
}

func (d *DisputeModel) TableName() string {
	return "disputes"
}

func (d *DisputeModel) ToDomain() *domain.Dispute {
	metadata := make(map[string]string)
	if d.Metadata != nil {
		metadata = map[string]string(d.Metadata)
	}

	return &domain.Dispute{
		ID:                    d.ID,
		StripeDisputeID:       d.StripeDisputeID,
		StripeChargeID:        d.StripeChargeID,
		PaymentID:             d.PaymentID,
		Amount:                d.Amount,
		Currency:              d.Currency,
		Reason:                domain.DisputeReason(d.Reason),
		Status:                domain.DisputeStatus(d.Status),
		EvidenceDueBy:         d.EvidenceDueBy,
		IsChargeRefundable:    d.IsChargeRefundable,
		LiveMode:              d.LiveMode,
		Metadata:              metadata,
		NetworkReasonCode:     d.NetworkReasonCode,
		CreatedAt:             d.CreatedAt,
		UpdatedAt:             d.UpdatedAt,
	}
}

func (d *DisputeModel) FromDomain(domainDispute *domain.Dispute) {
	d.ID = domainDispute.ID
	d.StripeDisputeID = domainDispute.StripeDisputeID
	d.StripeChargeID = domainDispute.StripeChargeID
	d.PaymentID = domainDispute.PaymentID
	d.Amount = domainDispute.Amount
	d.Currency = domainDispute.Currency
	d.Reason = string(domainDispute.Reason)
	d.Status = string(domainDispute.Status)
	d.EvidenceDueBy = domainDispute.EvidenceDueBy
	d.IsChargeRefundable = domainDispute.IsChargeRefundable
	d.LiveMode = domainDispute.LiveMode
	d.Metadata = MetadataJSON(domainDispute.Metadata)
	d.NetworkReasonCode = domainDispute.NetworkReasonCode
	d.CreatedAt = domainDispute.CreatedAt
	d.UpdatedAt = domainDispute.UpdatedAt
}
