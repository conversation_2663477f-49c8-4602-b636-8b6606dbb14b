package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
)

// PricingRulesJSON is a custom type for handling pricing rules JSON in GORM
type PricingRulesJSON domain.PricingRules

// Scan implements sql.Scanner interface
func (p *PricingRulesJSON) Scan(value interface{}) error {
	if value == nil {
		*p = PricingRulesJSON{}
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, p)
	case string:
		return json.Unmarshal([]byte(v), p)
	default:
		return errors.New("cannot scan into PricingRulesJSON")
	}
}

func (p PricingRulesJSON) Value() (driver.Value, error) {
	return json.Marshal(p)
}

type ParkingLotConfigModel struct {
	ID             uuid.UUID        `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	ParkingLotID   uuid.UUID        `gorm:"type:uuid;not null;index" json:"parking_lot_id"`
	ConfigName     string           `gorm:"not null" json:"config_name"`
	IsActive       bool             `gorm:"default:false;index" json:"is_active"`
	PricingRules   PricingRulesJSON `gorm:"type:jsonb;not null" json:"pricing_rules"`
	EffectiveFrom  time.Time        `gorm:"not null;index" json:"effective_from"`
	EffectiveUntil *time.Time       `gorm:"index" json:"effective_until"`
	CreatedAt      time.Time        `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt      time.Time        `gorm:"autoUpdateTime" json:"updated_at"`
	CreatedBy      uuid.UUID        `gorm:"type:uuid;not null" json:"created_by"`
	DeletedAt      *time.Time       `gorm:"index" json:"deleted_at"`

	ParkingLot ParkingLotModel `gorm:"foreignKey:ParkingLotID" json:"parking_lot,omitempty"`
}

func (ParkingLotConfigModel) TableName() string {
	return "parking_lot_configs"
}

func (p *ParkingLotConfigModel) ToDomain() *domain.ParkingLotConfig {
	return &domain.ParkingLotConfig{
		ID:             p.ID,
		ParkingLotID:   p.ParkingLotID,
		ConfigName:     p.ConfigName,
		IsActive:       p.IsActive,
		PricingRules:   domain.PricingRules(p.PricingRules),
		EffectiveFrom:  p.EffectiveFrom,
		EffectiveUntil: p.EffectiveUntil,
		CreatedAt:      p.CreatedAt,
		UpdatedAt:      p.UpdatedAt,
		CreatedBy:      p.CreatedBy,
		DeletedAt:      p.DeletedAt,
	}
}

func (p *ParkingLotConfigModel) FromDomain(domainConfig *domain.ParkingLotConfig) {
	p.ID = domainConfig.ID
	p.ParkingLotID = domainConfig.ParkingLotID
	p.ConfigName = domainConfig.ConfigName
	p.IsActive = domainConfig.IsActive
	p.PricingRules = PricingRulesJSON(domainConfig.PricingRules)
	p.EffectiveFrom = domainConfig.EffectiveFrom
	p.EffectiveUntil = domainConfig.EffectiveUntil
	p.CreatedAt = domainConfig.CreatedAt
	p.UpdatedAt = domainConfig.UpdatedAt
	p.CreatedBy = domainConfig.CreatedBy
	p.DeletedAt = domainConfig.DeletedAt
}
