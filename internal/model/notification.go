package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
)

type NotificationModel struct {
	ID           uuid.UUID  `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	UserID       uuid.UUID  `gorm:"type:uuid;not null;index" json:"user_id"`
	Type         string     `gorm:"type:notification_type;not null" json:"type"`
	Title        string     `gorm:"size:200;not null" json:"title"`
	Message      string     `gorm:"not null" json:"message"`
	SessionID    *uuid.UUID `gorm:"type:uuid" json:"session_id"`
	PaymentID    *uuid.UUID `gorm:"type:uuid" json:"payment_id"`
	ParkingLotID *uuid.UUID `gorm:"type:uuid" json:"parking_lot_id"`
	IsSent       bool       `gorm:"default:false" json:"is_sent"`
	SentAt       *time.Time `json:"sent_at"`
	CreatedAt    time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt    time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt    *time.Time `gorm:"index" json:"deleted_at"`

	User       UserModel        `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Session    *SessionModel    `gorm:"foreignKey:SessionID" json:"session,omitempty"`
	Payment    *PaymentModel    `gorm:"foreignKey:PaymentID" json:"payment,omitempty"`
	ParkingLot *ParkingLotModel `gorm:"foreignKey:ParkingLotID" json:"parking_lot,omitempty"`
}

func (NotificationModel) TableName() string {
	return "notifications"
}

func (n *NotificationModel) ToDomain() *domain.Notification {
	return &domain.Notification{
		ID:           n.ID,
		UserID:       n.UserID,
		Type:         domain.NotificationType(n.Type),
		Title:        n.Title,
		Message:      n.Message,
		SessionID:    n.SessionID,
		PaymentID:    n.PaymentID,
		ParkingLotID: n.ParkingLotID,
		IsSent:       n.IsSent,
		SentAt:       n.SentAt,
		CreatedAt:    n.CreatedAt,
		UpdatedAt:    n.UpdatedAt,
		DeletedAt:    n.DeletedAt,
	}
}

func (n *NotificationModel) FromDomain(domainNotification *domain.Notification) {
	n.ID = domainNotification.ID
	n.UserID = domainNotification.UserID
	n.Type = string(domainNotification.Type)
	n.Title = domainNotification.Title
	n.Message = domainNotification.Message
	n.SessionID = domainNotification.SessionID
	n.PaymentID = domainNotification.PaymentID
	n.ParkingLotID = domainNotification.ParkingLotID
	n.IsSent = domainNotification.IsSent
	n.SentAt = domainNotification.SentAt
	n.CreatedAt = domainNotification.CreatedAt
	n.UpdatedAt = domainNotification.UpdatedAt
	n.DeletedAt = domainNotification.DeletedAt
}
