package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
)

type PaymentMethodModel struct {
	ID                    uuid.UUID  `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	UserID                uuid.UUID  `gorm:"type:uuid;not null;index" json:"user_id"`
	StripePaymentMethodID string     `gorm:"not null;uniqueIndex" json:"stripe_payment_method_id"`
	Type                  string     `gorm:"type:payment_method;not null" json:"type"`
	Brand                 *string    `gorm:"size:50" json:"brand"`
	Last4                 *string    `gorm:"size:4" json:"last4"`
	ExpiryMonth           *int       `json:"expiry_month"`
	ExpiryYear            *int       `json:"expiry_year"`
	IsDefault             bool       `gorm:"default:false;index" json:"is_default"`
	CreatedAt             time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt             time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt             *time.Time `gorm:"index" json:"deleted_at"`

	User UserModel `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

func (PaymentMethodModel) TableName() string {
	return "payment_methods"
}

func (pm *PaymentMethodModel) ToDomain() *domain.PaymentMethod {
	return &domain.PaymentMethod{
		ID:                    pm.ID,
		UserID:                pm.UserID,
		StripePaymentMethodID: pm.StripePaymentMethodID,
		Type:                  domain.PaymentMethodType(pm.Type),
		Brand:                 pm.Brand,
		Last4:                 pm.Last4,
		ExpiryMonth:           pm.ExpiryMonth,
		ExpiryYear:            pm.ExpiryYear,
		IsDefault:             pm.IsDefault,
		CreatedAt:             pm.CreatedAt,
		UpdatedAt:             pm.UpdatedAt,
		DeletedAt:             pm.DeletedAt,
	}
}

func (pm *PaymentMethodModel) FromDomain(domainPaymentMethod *domain.PaymentMethod) {
	pm.ID = domainPaymentMethod.ID
	pm.UserID = domainPaymentMethod.UserID
	pm.StripePaymentMethodID = domainPaymentMethod.StripePaymentMethodID
	pm.Type = string(domainPaymentMethod.Type)
	pm.Brand = domainPaymentMethod.Brand
	pm.Last4 = domainPaymentMethod.Last4
	pm.ExpiryMonth = domainPaymentMethod.ExpiryMonth
	pm.ExpiryYear = domainPaymentMethod.ExpiryYear
	pm.IsDefault = domainPaymentMethod.IsDefault
	pm.CreatedAt = domainPaymentMethod.CreatedAt
	pm.UpdatedAt = domainPaymentMethod.UpdatedAt
	pm.DeletedAt = domainPaymentMethod.DeletedAt
}
