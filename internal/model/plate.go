package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
)

type PlateModel struct {
	ID             uuid.UUID  `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	UserID         uuid.UUID  `gorm:"type:uuid;not null;index" json:"user_id"`
	Region         string     `gorm:"size:10;not null" json:"region"`
	Classification string     `gorm:"size:3;not null" json:"classification"`
	Hiragana       string     `gorm:"size:1;not null" json:"hiragana"`
	SerialNumber   string     `gorm:"size:4;not null" json:"serial_number"`
	PlateNumber    string     `gorm:"size:50;uniqueIndex" json:"plate_number"`
	PlateType      string     `gorm:"type:plate_type;default:normal" json:"plate_type"`
	IsActive       bool       `gorm:"default:true" json:"is_active"`
	CreatedAt      time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt      time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt      *time.Time `gorm:"index" json:"deleted_at"`

	User     UserModel      `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Sessions []SessionModel `gorm:"foreignKey:PlateID" json:"sessions,omitempty"`
	Bookings []BookingModel `gorm:"foreignKey:PlateID" json:"bookings,omitempty"`
}

func (PlateModel) TableName() string {
	return "plates"
}

func (p *PlateModel) ToDomain() *domain.Plate {
	return &domain.Plate{
		ID:             p.ID,
		UserID:         p.UserID,
		Region:         p.Region,
		Classification: p.Classification,
		Hiragana:       p.Hiragana,
		SerialNumber:   p.SerialNumber,
		PlateNumber:    p.PlateNumber,
		PlateType:      domain.PlateType(p.PlateType),
		IsActive:       p.IsActive,
		CreatedAt:      p.CreatedAt,
		UpdatedAt:      p.UpdatedAt,
		DeletedAt:      p.DeletedAt,
	}
}

func (p *PlateModel) FromDomain(domainPlate *domain.Plate) {
	p.ID = domainPlate.ID
	p.UserID = domainPlate.UserID
	p.Region = domainPlate.Region
	p.Classification = domainPlate.Classification
	p.Hiragana = domainPlate.Hiragana
	p.SerialNumber = domainPlate.SerialNumber
	p.PlateNumber = domainPlate.PlateNumber
	p.PlateType = string(domainPlate.PlateType)
	p.IsActive = domainPlate.IsActive
	p.CreatedAt = domainPlate.CreatedAt
	p.UpdatedAt = domainPlate.UpdatedAt
	p.DeletedAt = domainPlate.DeletedAt
}
