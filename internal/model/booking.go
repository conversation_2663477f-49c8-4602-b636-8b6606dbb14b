package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
)

type BookingModel struct {
	ID              uuid.UUID  `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	UserID          uuid.UUID  `gorm:"type:uuid;not null;index" json:"user_id"`
	PlateID         uuid.UUID  `gorm:"type:uuid;not null;index" json:"plate_id"`
	ParkingLotID    uuid.UUID  `gorm:"type:uuid;not null;index" json:"parking_lot_id"`
	StartTime       time.Time  `gorm:"not null" json:"start_time"`
	EndTime         time.Time  `gorm:"not null" json:"end_time"`
	HourlyRate      int        `gorm:"not null" json:"hourly_rate"`
	TotalAmount     int        `gorm:"not null" json:"total_amount"`
	Status          string     `gorm:"type:booking_status;default:confirmed" json:"status"`
	CancelledAt     *time.Time `json:"cancelled_at"`
	CancellationFee int        `gorm:"default:0" json:"cancellation_fee"`
	CreatedAt       time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt       time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt       *time.Time `gorm:"index" json:"deleted_at"`

	User       UserModel       `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Plate      PlateModel      `gorm:"foreignKey:PlateID" json:"plate,omitempty"`
	ParkingLot ParkingLotModel `gorm:"foreignKey:ParkingLotID" json:"parking_lot,omitempty"`
}

func (BookingModel) TableName() string {
	return "bookings"
}

func (b *BookingModel) ToDomain() *domain.Booking {
	return &domain.Booking{
		ID:              b.ID,
		UserID:          b.UserID,
		PlateID:         b.PlateID,
		ParkingLotID:    b.ParkingLotID,
		StartTime:       b.StartTime,
		EndTime:         b.EndTime,
		HourlyRate:      b.HourlyRate,
		TotalAmount:     b.TotalAmount,
		Status:          domain.BookingStatus(b.Status),
		CancelledAt:     b.CancelledAt,
		CancellationFee: b.CancellationFee,
		CreatedAt:       b.CreatedAt,
		UpdatedAt:       b.UpdatedAt,
		DeletedAt:       b.DeletedAt,
	}
}

func (b *BookingModel) FromDomain(domainBooking *domain.Booking) {
	b.ID = domainBooking.ID
	b.UserID = domainBooking.UserID
	b.PlateID = domainBooking.PlateID
	b.ParkingLotID = domainBooking.ParkingLotID
	b.StartTime = domainBooking.StartTime
	b.EndTime = domainBooking.EndTime
	b.HourlyRate = domainBooking.HourlyRate
	b.TotalAmount = domainBooking.TotalAmount
	b.Status = string(domainBooking.Status)
	b.CancelledAt = domainBooking.CancelledAt
	b.CancellationFee = domainBooking.CancellationFee
	b.CreatedAt = domainBooking.CreatedAt
	b.UpdatedAt = domainBooking.UpdatedAt
	b.DeletedAt = domainBooking.DeletedAt
}
