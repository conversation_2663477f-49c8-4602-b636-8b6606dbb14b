package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
)

type StringArray []string

func (s *StringArray) Scan(value interface{}) error {
	if value == nil {
		*s = StringArray{}
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, s)
	case string:
		return json.Unmarshal([]byte(v), s)
	default:
		return errors.New("cannot scan into StringArray")
	}
}

func (s StringArray) Value() (driver.Value, error) {
	if len(s) == 0 {
		return "[]", nil
	}
	return json.Marshal(s)
}

type ParkingLotModel struct {
	ID            uuid.UUID   `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	Name          string      `gorm:"not null" json:"name"`
	Address       string      `gorm:"not null" json:"address"`
	Latitude      float64     `gorm:"type:decimal(10,8);not null" json:"latitude"`
	Longitude     float64     `gorm:"type:decimal(11,8);not null" json:"longitude"`
	TotalSpots    int         `gorm:"not null" json:"total_spots"`
	HeightLimitCm *int        `json:"height_limit_cm"`
	HourlyRate    int         `gorm:"not null" json:"hourly_rate"`
	DailyMaxRate  *int        `json:"daily_max_rate"`
	FreeMinutes   int         `gorm:"default:30" json:"free_minutes"`
	Is24h         bool        `gorm:"default:true" json:"is_24h"`
	OpenTime      *string     `json:"open_time"`
	CloseTime     *string     `json:"close_time"`
	Features      StringArray `gorm:"type:json" json:"features"`
	Images        StringArray `gorm:"type:json" json:"images"`
	Status        string      `gorm:"type:parking_lot_status;default:active" json:"status"`
	OperatorName  *string     `json:"operator_name"`
	ContactPhone  *string     `json:"contact_phone"`
	CreatedAt     time.Time   `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time   `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt     *time.Time  `gorm:"index" json:"deleted_at"`

	Sessions []SessionModel `gorm:"foreignKey:ParkingLotID" json:"sessions,omitempty"`
	Bookings []BookingModel `gorm:"foreignKey:ParkingLotID" json:"bookings,omitempty"`
}

func (ParkingLotModel) TableName() string {
	return "parking_lots"
}

func (p *ParkingLotModel) ToDomain() *domain.ParkingLot {
	return &domain.ParkingLot{
		ID:            p.ID,
		Name:          p.Name,
		Address:       p.Address,
		Latitude:      p.Latitude,
		Longitude:     p.Longitude,
		TotalSpots:    p.TotalSpots,
		HeightLimitCm: p.HeightLimitCm,
		HourlyRate:    p.HourlyRate,
		DailyMaxRate:  p.DailyMaxRate,
		FreeMinutes:   p.FreeMinutes,
		Is24h:         p.Is24h,
		OpenTime:      p.OpenTime,
		CloseTime:     p.CloseTime,
		Features:      []string(p.Features),
		Images:        []string(p.Images),
		Status:        domain.LotStatus(p.Status),
		OperatorName:  p.OperatorName,
		ContactPhone:  p.ContactPhone,
		CreatedAt:     p.CreatedAt,
		UpdatedAt:     p.UpdatedAt,
		DeletedAt:     p.DeletedAt,
	}
}

func (p *ParkingLotModel) FromDomain(domainParkingLot *domain.ParkingLot) {
	p.ID = domainParkingLot.ID
	p.Name = domainParkingLot.Name
	p.Address = domainParkingLot.Address
	p.Latitude = domainParkingLot.Latitude
	p.Longitude = domainParkingLot.Longitude
	p.TotalSpots = domainParkingLot.TotalSpots
	p.HeightLimitCm = domainParkingLot.HeightLimitCm
	p.HourlyRate = domainParkingLot.HourlyRate
	p.DailyMaxRate = domainParkingLot.DailyMaxRate
	p.FreeMinutes = domainParkingLot.FreeMinutes
	p.Is24h = domainParkingLot.Is24h
	p.OpenTime = domainParkingLot.OpenTime
	p.CloseTime = domainParkingLot.CloseTime
	p.Features = StringArray(domainParkingLot.Features)
	p.Images = StringArray(domainParkingLot.Images)
	p.Status = string(domainParkingLot.Status)
	p.OperatorName = domainParkingLot.OperatorName
	p.ContactPhone = domainParkingLot.ContactPhone
	p.CreatedAt = domainParkingLot.CreatedAt
	p.UpdatedAt = domainParkingLot.UpdatedAt
	p.DeletedAt = domainParkingLot.DeletedAt
}
