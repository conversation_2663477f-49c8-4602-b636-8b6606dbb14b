package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
)

type PaymentModel struct {
	ID                    uuid.UUID  `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	SessionID             uuid.UUID  `gorm:"type:uuid;not null;uniqueIndex" json:"session_id"`
	UserID                uuid.UUID  `gorm:"type:uuid;not null;index" json:"user_id"`
	Amount                int        `gorm:"not null" json:"amount"`
	Currency              string     `gorm:"default:JPY" json:"currency"`
	StripePaymentIntentID *string    `gorm:"uniqueIndex" json:"stripe_payment_intent_id"`
	StripePaymentLinkID   *string    `json:"stripe_payment_link_id"`
	StripeStatus          *string    `json:"stripe_status"`
	PaymentMethodType     *string    `json:"payment_method_type"`
	CardLast4             *string    `json:"card_last4"`
	CardBrand             *string    `json:"card_brand"`
	Status                string     `gorm:"type:payment_status;default:pending" json:"status"`
	PaidAt                *time.Time `json:"paid_at"`
	ReceiptURL            *string    `json:"receipt_url"`
	InvoiceNumber         *string    `gorm:"uniqueIndex" json:"invoice_number"`
	FailureReason         *string    `json:"failure_reason"`
	RetryCount            int        `gorm:"default:0" json:"retry_count"`
	CreatedAt             time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt             time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt             *time.Time `gorm:"index" json:"deleted_at"`

	Session SessionModel `gorm:"foreignKey:SessionID" json:"session,omitempty"`
	User    UserModel    `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

func (PaymentModel) TableName() string {
	return "payments"
}

func (p *PaymentModel) ToDomain() *domain.Payment {
	return &domain.Payment{
		ID:                    p.ID,
		SessionID:             p.SessionID,
		UserID:                p.UserID,
		Amount:                p.Amount,
		Currency:              p.Currency,
		StripePaymentIntentID: p.StripePaymentIntentID,
		StripePaymentLinkID:   p.StripePaymentLinkID,
		StripeStatus:          p.StripeStatus,
		PaymentMethodType:     p.PaymentMethodType,
		CardLast4:             p.CardLast4,
		CardBrand:             p.CardBrand,
		Status:                domain.PaymentStatus(p.Status),
		PaidAt:                p.PaidAt,
		ReceiptURL:            p.ReceiptURL,
		InvoiceNumber:         p.InvoiceNumber,
		FailureReason:         p.FailureReason,
		RetryCount:            p.RetryCount,
		CreatedAt:             p.CreatedAt,
		UpdatedAt:             p.UpdatedAt,
		DeletedAt:             p.DeletedAt,
	}
}

func (p *PaymentModel) FromDomain(domainPayment *domain.Payment) {
	p.ID = domainPayment.ID
	p.SessionID = domainPayment.SessionID
	p.UserID = domainPayment.UserID
	p.Amount = domainPayment.Amount
	p.Currency = domainPayment.Currency
	p.StripePaymentIntentID = domainPayment.StripePaymentIntentID
	p.StripePaymentLinkID = domainPayment.StripePaymentLinkID
	p.StripeStatus = domainPayment.StripeStatus
	p.PaymentMethodType = domainPayment.PaymentMethodType
	p.CardLast4 = domainPayment.CardLast4
	p.CardBrand = domainPayment.CardBrand
	p.Status = string(domainPayment.Status)
	p.PaidAt = domainPayment.PaidAt
	p.ReceiptURL = domainPayment.ReceiptURL
	p.InvoiceNumber = domainPayment.InvoiceNumber
	p.FailureReason = domainPayment.FailureReason
	p.RetryCount = domainPayment.RetryCount
	p.CreatedAt = domainPayment.CreatedAt
	p.UpdatedAt = domainPayment.UpdatedAt
	p.DeletedAt = domainPayment.DeletedAt
}
