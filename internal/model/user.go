package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
)

type UserModel struct {
	ID                     uuid.UUID  `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	Username               string     `gorm:"uniqueIndex;not null;size:50" json:"username"`
	Email                  string     `gorm:"uniqueIndex;not null;size:255" json:"email"`
	PasswordHash           string     `gorm:"not null;size:255" json:"-"`
	Name                   string     `gorm:"not null;size:200" json:"name"`
	Phone                  *string    `gorm:"size:20" json:"phone"`
	PreferredLanguage      string     `gorm:"type:varchar(2);not null;default:'ja';check:preferred_language IN ('ja', 'en')" json:"preferred_language"`
	Role                   string     `gorm:"type:user_role;default:user" json:"role"`
	Status                 string     `gorm:"type:user_status;default:active" json:"status"`
	StripeCustomerID       *string    `gorm:"uniqueIndex;size:255" json:"stripe_customer_id"`
	DefaultPaymentMethodID *string    `gorm:"size:255" json:"default_payment_method_id"`
	AutoPaymentEnabled     bool       `gorm:"default:true" json:"auto_payment_enabled"`
	NotifyEmail            bool       `gorm:"default:true" json:"notify_email"`
	NotifyPush             bool       `gorm:"default:true" json:"notify_push"`
	EmailVerified          bool       `gorm:"default:false" json:"email_verified"`
	LastLoginAt            *time.Time `json:"last_login_at"`
	CreatedAt              time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt              time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt              *time.Time `gorm:"index" json:"deleted_at"`

	Plates             []PlateModel             `gorm:"foreignKey:UserID" json:"plates,omitempty"`
	Sessions           []SessionModel           `gorm:"foreignKey:UserID" json:"sessions,omitempty"`
	UserSessions       []UserSessionModel       `gorm:"foreignKey:UserID" json:"user_sessions,omitempty"`
	VerificationTokens []VerificationTokenModel `gorm:"foreignKey:UserID" json:"verification_tokens,omitempty"`
	Payments           []PaymentModel           `gorm:"foreignKey:UserID" json:"payments,omitempty"`
	Bookings           []BookingModel           `gorm:"foreignKey:UserID" json:"bookings,omitempty"`
	Notifications      []NotificationModel      `gorm:"foreignKey:UserID" json:"notifications,omitempty"`
}

func (UserModel) TableName() string {
	return "users"
}

func (u *UserModel) ToDomain() *domain.User {
	return &domain.User{
		ID:                     u.ID,
		Username:               u.Username,
		Email:                  u.Email,
		PasswordHash:           u.PasswordHash,
		Name:                   u.Name,
		Phone:                  u.Phone,
		PreferredLanguage:      domain.LanguageCode(u.PreferredLanguage),
		Role:                   domain.UserRole(u.Role),
		Status:                 domain.UserStatus(u.Status),
		StripeCustomerID:       u.StripeCustomerID,
		DefaultPaymentMethodID: u.DefaultPaymentMethodID,
		AutoPaymentEnabled:     u.AutoPaymentEnabled,
		NotifyEmail:            u.NotifyEmail,
		NotifyPush:             u.NotifyPush,
		EmailVerified:          u.EmailVerified,
		LastLoginAt:            u.LastLoginAt,
		CreatedAt:              u.CreatedAt,
		UpdatedAt:              u.UpdatedAt,
		DeletedAt:              u.DeletedAt,
	}
}

func (u *UserModel) FromDomain(domainUser *domain.User) {
	u.ID = domainUser.ID
	u.Username = domainUser.Username
	u.Email = domainUser.Email
	u.PasswordHash = domainUser.PasswordHash
	u.Name = domainUser.Name
	u.Phone = domainUser.Phone
	u.PreferredLanguage = string(domainUser.PreferredLanguage)
	u.Role = string(domainUser.Role)
	u.Status = string(domainUser.Status)
	u.StripeCustomerID = domainUser.StripeCustomerID
	u.DefaultPaymentMethodID = domainUser.DefaultPaymentMethodID
	u.AutoPaymentEnabled = domainUser.AutoPaymentEnabled
	u.NotifyEmail = domainUser.NotifyEmail
	u.NotifyPush = domainUser.NotifyPush
	u.EmailVerified = domainUser.EmailVerified
	u.LastLoginAt = domainUser.LastLoginAt
	u.CreatedAt = domainUser.CreatedAt
	u.UpdatedAt = domainUser.UpdatedAt
	u.DeletedAt = domainUser.DeletedAt
}
