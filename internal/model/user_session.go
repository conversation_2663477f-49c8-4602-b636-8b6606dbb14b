package model

import (
	"net"
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
	"gorm.io/gorm"
)

type UserSessionModel struct {
	ID               uuid.UUID `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	UserID           uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`
	DeviceID         string    `gorm:"not null;size:255;index" json:"device_id"`
	DeviceName       string    `gorm:"size:255" json:"device_name"`
	DeviceType       string    `gorm:"size:50" json:"device_type"`
	IPAddress        string    `gorm:"type:inet" json:"ip_address"`
	UserAgent        string    `gorm:"type:text" json:"user_agent"`
	RefreshTokenHash string    `gorm:"not null;size:255;index" json:"-"`
	ExpiresAt        time.Time `gorm:"not null;index" json:"expires_at"`
	LastUsedAt       time.Time `gorm:"not null;default:CURRENT_TIMESTAMP" json:"last_used_at"`
	CreatedAt        time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt        time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	User UserModel `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"user,omitempty"`
}

func (UserSessionModel) TableName() string {
	return "user_sessions"
}

func (s *UserSessionModel) BeforeCreate(tx *gorm.DB) error {
	// Ensure unique constraint on user_id + device_id
	return nil
}

func (s *UserSessionModel) ToDomain() *domain.UserSession {
	var ip net.IP
	if s.IPAddress != "" {
		ip = net.ParseIP(s.IPAddress)
	}

	return &domain.UserSession{
		ID:               s.ID,
		UserID:           s.UserID,
		DeviceID:         s.DeviceID,
		DeviceName:       s.DeviceName,
		DeviceType:       s.DeviceType,
		IPAddress:        ip,
		UserAgent:        s.UserAgent,
		RefreshTokenHash: s.RefreshTokenHash,
		ExpiresAt:        s.ExpiresAt,
		LastUsedAt:       s.LastUsedAt,
		CreatedAt:        s.CreatedAt,
		UpdatedAt:        s.UpdatedAt,
	}
}

func (s *UserSessionModel) FromDomain(domainSession *domain.UserSession) {
	s.ID = domainSession.ID
	s.UserID = domainSession.UserID
	s.DeviceID = domainSession.DeviceID
	s.DeviceName = domainSession.DeviceName
	s.DeviceType = domainSession.DeviceType
	if domainSession.IPAddress != nil {
		s.IPAddress = domainSession.IPAddress.String()
	} else {
		s.IPAddress = "0.0.0.0"
	}
	s.UserAgent = domainSession.UserAgent
	s.RefreshTokenHash = domainSession.RefreshTokenHash
	s.ExpiresAt = domainSession.ExpiresAt
	s.LastUsedAt = domainSession.LastUsedAt
	s.CreatedAt = domainSession.CreatedAt
	s.UpdatedAt = domainSession.UpdatedAt
}

func (s *UserSessionModel) IsExpired() bool {
	return time.Now().After(s.ExpiresAt)
}

func (s *UserSessionModel) IsValid() bool {
	return !s.IsExpired()
}

func (s *UserSessionModel) ToSessionResponse(isCurrent bool) *domain.SessionResponse {
	return &domain.SessionResponse{
		ID:         s.ID,
		DeviceID:   s.DeviceID,
		DeviceName: s.DeviceName,
		DeviceType: s.DeviceType,
		IPAddress:  s.IPAddress,
		LastUsedAt: s.LastUsedAt,
		CreatedAt:  s.CreatedAt,
		IsCurrent:  isCurrent,
	}
}
