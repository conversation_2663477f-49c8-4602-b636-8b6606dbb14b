package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
)

type SessionModel struct {
	ID                  uuid.UUID  `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	UserID              *uuid.UUID `gorm:"type:uuid;index" json:"user_id"`
	PlateID             *uuid.UUID `gorm:"type:uuid;index" json:"plate_id"`
	ParkingLotID        uuid.UUID  `gorm:"type:uuid;not null;index" json:"parking_lot_id"`
	EntryTime           time.Time  `gorm:"not null;default:NOW()" json:"entry_time"`
	ExitTime            *time.Time `json:"exit_time"`
	DurationMinutes     *int       `json:"duration_minutes"`
	Amount              *int       `json:"amount"`
	DiscountAmount      int        `gorm:"default:0" json:"discount_amount"`
	Status              string     `gorm:"type:session_status;default:active" json:"status"`
	IsPaid              bool       `gorm:"default:false" json:"is_paid"`
	EntryImageURL       *string    `json:"entry_image_url"`
	ExitImageURL        *string    `json:"exit_image_url"`
	DetectionConfidence *float64   `gorm:"type:decimal(5,2)" json:"detection_confidence"`
	ErrorMessage        *string    `json:"error_message"`
	ManualOverride      bool       `gorm:"default:false" json:"manual_override"`
	CreatedAt           time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt           time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt           *time.Time `gorm:"index" json:"deleted_at"`

	User       *UserModel      `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Plate      *PlateModel     `gorm:"foreignKey:PlateID" json:"plate,omitempty"`
	ParkingLot ParkingLotModel `gorm:"foreignKey:ParkingLotID" json:"parking_lot,omitempty"`
	Payment    *PaymentModel   `gorm:"foreignKey:SessionID" json:"payment,omitempty"`
}

func (SessionModel) TableName() string {
	return "sessions"
}

func (s *SessionModel) ToDomain() *domain.Session {
	return &domain.Session{
		ID:                  s.ID,
		UserID:              s.UserID,
		PlateID:             s.PlateID,
		ParkingLotID:        s.ParkingLotID,
		EntryTime:           s.EntryTime,
		ExitTime:            s.ExitTime,
		DurationMinutes:     s.DurationMinutes,
		Amount:              s.Amount,
		DiscountAmount:      s.DiscountAmount,
		Status:              domain.SessionStatus(s.Status),
		IsPaid:              s.IsPaid,
		EntryImageURL:       s.EntryImageURL,
		ExitImageURL:        s.ExitImageURL,
		DetectionConfidence: s.DetectionConfidence,
		ErrorMessage:        s.ErrorMessage,
		ManualOverride:      s.ManualOverride,
		CreatedAt:           s.CreatedAt,
		UpdatedAt:           s.UpdatedAt,
		DeletedAt:           s.DeletedAt,
	}
}

func (s *SessionModel) FromDomain(domainSession *domain.Session) {
	s.ID = domainSession.ID
	s.UserID = domainSession.UserID
	s.PlateID = domainSession.PlateID
	s.ParkingLotID = domainSession.ParkingLotID
	s.EntryTime = domainSession.EntryTime
	s.ExitTime = domainSession.ExitTime
	s.DurationMinutes = domainSession.DurationMinutes
	s.Amount = domainSession.Amount
	s.DiscountAmount = domainSession.DiscountAmount
	s.Status = string(domainSession.Status)
	s.IsPaid = domainSession.IsPaid
	s.EntryImageURL = domainSession.EntryImageURL
	s.ExitImageURL = domainSession.ExitImageURL
	s.DetectionConfidence = domainSession.DetectionConfidence
	s.ErrorMessage = domainSession.ErrorMessage
	s.ManualOverride = domainSession.ManualOverride
	s.CreatedAt = domainSession.CreatedAt
	s.UpdatedAt = domainSession.UpdatedAt
	s.DeletedAt = domainSession.DeletedAt
}
