package gateway

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/smooth-inc/backend/internal/domain"
)

type PricingGateway interface {
	CalculateFee(ctx context.Context, req CalculateFeeRequest) (*CalculateFeeResponse, error)
	ValidatePricingRules(ctx context.Context, rules domain.PricingRules) error
}

type CalculateFeeRequest struct {
	Entry        time.Time           `json:"entry"`
	Exit         time.Time           `json:"exit"`
	PricingRules domain.PricingRules `json:"pricing_rules"`
}

type CalculateFeeResponse struct {
	Fee             int          `json:"fee"`
	Breakdown       FeeBreakdown `json:"breakdown"`
	AppliedRules    []string     `json:"applied_rules"`
	FreeMinutesUsed int          `json:"free_minutes_used"`
	TotalMinutes    int          `json:"total_minutes"`
	BillableMinutes int          `json:"billable_minutes"`
}

type FeeBreakdown struct {
	BaseFee         int `json:"base_fee"`
	NightCapApplied int `json:"night_cap_applied"`
	DailyCapApplied int `json:"daily_cap_applied"`
	OverrideFee     int `json:"override_fee"`
	DiscountAmount  int `json:"discount_amount"`
	FinalFee        int `json:"final_fee"`
}

type pricingGateway struct {
	lambdaURL  string
	httpClient *http.Client
	timeout    time.Duration
}

type PricingGatewayConfig struct {
	LambdaURL string
	Timeout   time.Duration
}

func NewPricingGateway(config PricingGatewayConfig) PricingGateway {
	if config.Timeout == 0 {
		config.Timeout = 10 * time.Second
	}

	return &pricingGateway{
		lambdaURL: config.LambdaURL,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
		timeout: config.Timeout,
	}
}

func (g *pricingGateway) CalculateFee(ctx context.Context, req CalculateFeeRequest) (*CalculateFeeResponse, error) {
	payload, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", g.lambdaURL+"/calculate-fee", bytes.NewBuffer(payload))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := g.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("pricing service returned status %d", resp.StatusCode)
	}

	var response CalculateFeeResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &response, nil
}

func (g *pricingGateway) ValidatePricingRules(ctx context.Context, rules domain.PricingRules) error {
	payload, err := json.Marshal(map[string]interface{}{
		"pricing_rules": rules,
		"validate_only": true,
	})
	if err != nil {
		return fmt.Errorf("failed to marshal validation request: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", g.lambdaURL+"/validate-rules", bytes.NewBuffer(payload))
	if err != nil {
		return fmt.Errorf("failed to create validation request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := g.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to execute validation request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errorResp struct {
			Error string `json:"error"`
		}
		if err := json.NewDecoder(resp.Body).Decode(&errorResp); err == nil {
			return fmt.Errorf("pricing rules validation failed: %s", errorResp.Error)
		}
		return fmt.Errorf("pricing rules validation failed with status %d", resp.StatusCode)
	}

	return nil
}

type MockPricingGateway struct {
	CalculateFeeFunc         func(ctx context.Context, req CalculateFeeRequest) (*CalculateFeeResponse, error)
	ValidatePricingRulesFunc func(ctx context.Context, rules domain.PricingRules) error
}

func NewMockPricingGateway() *MockPricingGateway {
	return &MockPricingGateway{
		CalculateFeeFunc: func(ctx context.Context, req CalculateFeeRequest) (*CalculateFeeResponse, error) {
			duration := req.Exit.Sub(req.Entry)
			hours := int(duration.Hours())
			if duration.Minutes() > float64(hours*60) {
				hours++ // Round up partial hours
			}

			fee := hours * 200
			totalMinutes := int(duration.Minutes())
			freeMinutes := req.PricingRules.InitialFreeMinutes
			billableMinutes := totalMinutes - freeMinutes
			if billableMinutes < 0 {
				billableMinutes = 0
				fee = 0
			}

			return &CalculateFeeResponse{
				Fee:             fee,
				TotalMinutes:    totalMinutes,
				FreeMinutesUsed: freeMinutes,
				BillableMinutes: billableMinutes,
				Breakdown: FeeBreakdown{
					BaseFee:  fee,
					FinalFee: fee,
				},
				AppliedRules: []string{"mock_hourly_rate"},
			}, nil
		},
		ValidatePricingRulesFunc: func(ctx context.Context, rules domain.PricingRules) error {
			// Mock validation always passes
			return nil
		},
	}
}

func (m *MockPricingGateway) CalculateFee(ctx context.Context, req CalculateFeeRequest) (*CalculateFeeResponse, error) {
	if m.CalculateFeeFunc != nil {
		return m.CalculateFeeFunc(ctx, req)
	}
	return nil, fmt.Errorf("mock CalculateFee not implemented")
}

func (m *MockPricingGateway) ValidatePricingRules(ctx context.Context, rules domain.PricingRules) error {
	if m.ValidatePricingRulesFunc != nil {
		return m.ValidatePricingRulesFunc(ctx, rules)
	}
	return fmt.Errorf("mock ValidatePricingRules not implemented")
}
