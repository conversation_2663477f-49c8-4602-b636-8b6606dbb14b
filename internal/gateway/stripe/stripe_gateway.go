package stripe

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/stripe/stripe-go/v76"
	"github.com/stripe/stripe-go/v76/customer"
	"github.com/stripe/stripe-go/v76/paymentintent"
	"github.com/stripe/stripe-go/v76/paymentlink"
	"github.com/stripe/stripe-go/v76/paymentmethod"
	"github.com/stripe/stripe-go/v76/setupintent"
	"github.com/stripe/stripe-go/v76/webhook"

	"github.com/smooth-inc/backend/internal/domain"
)

type Gateway interface {
	CreateCustomer(ctx context.Context, email, name string) (*Customer, error)
	CreateSetupIntent(ctx context.Context, customerID string) (*domain.SetupIntent, error)
	AttachPaymentMethodToCustomer(ctx context.Context, paymentMethodID, customerID string) error
	DetachPaymentMethodFromCustomer(ctx context.Context, paymentMethodID string) error
	GetPaymentMethod(ctx context.Context, paymentMethodID string) (*PaymentMethodInfo, error)
	ListCustomerPaymentMethods(ctx context.Context, customerID string) ([]*PaymentMethodInfo, error)
	SetDefaultPaymentMethod(ctx context.Context, customerID, paymentMethodID string) error
	CreateOffSessionPayment(ctx context.Context, req *OffSessionPaymentRequest) (*PaymentResult, error)
	CreatePaymentLink(ctx context.Context, amount int, currency, description string) (*PaymentLinkResult, error)
	CreatePaymentIntent(ctx context.Context, amount int, currency, customerID, paymentMethodID string) (*PaymentIntentInfo, error)
	ConfirmPaymentIntent(ctx context.Context, paymentIntentID string) (*PaymentConfirmResult, error)
	VerifyWebhook(ctx context.Context, payload []byte, signature string) (*WebhookEvent, error)
	GetPaymentIntent(ctx context.Context, paymentIntentID string) (*PaymentIntentInfo, error)
}

type StripeGateway struct {
	secretKey             string
	webhookEndpointSecret string
}

type Customer struct {
	ID    string
	Email string
	Name  string
}

type PaymentMethodInfo struct {
	ID          string
	Type        string
	Brand       *string
	Last4       *string
	ExpiryMonth *int
	ExpiryYear  *int
}

type OffSessionPaymentRequest struct {
	CustomerID      string
	PaymentMethodID string
	Amount          int64
	Currency        string
	Description     string
	Metadata        map[string]string
}

type PaymentResult struct {
	PaymentIntentID string
	Status          string
	Amount          int64
	Currency        string
	PaymentMethod   *PaymentMethodInfo
	ReceiptURL      *string
}

type PaymentLinkRequest struct {
	Amount      int64
	Currency    string
	Description string
	Metadata    map[string]string
}

type PaymentLinkResult struct {
	ID              string
	URL             string
	PaymentIntentID *string
}

type PaymentIntentInfo struct {
	ID            string
	Status        string
	Amount        int64
	Currency      string
	PaymentMethod *PaymentMethodInfo
	ReceiptURL    *string
}

type PaymentConfirmResult struct {
	ID         string
	Status     string
	ReceiptURL string
}

type WebhookEvent struct {
	Type string
	Data map[string]interface{}
}

func NewStripeGateway(secretKey, webhookSecret string) Gateway {
	stripe.Key = secretKey
	return &StripeGateway{
		secretKey:             secretKey,
		webhookEndpointSecret: webhookSecret,
	}
}

func (s *StripeGateway) CreateCustomer(ctx context.Context, email, name string) (*Customer, error) {
	params := &stripe.CustomerParams{
		Email: stripe.String(email),
		Name:  stripe.String(name),
	}

	result, err := customer.New(params)
	if err != nil {
		return nil, fmt.Errorf("failed to create Stripe customer: %w", err)
	}

	return &Customer{
		ID:    result.ID,
		Email: result.Email,
		Name:  result.Name,
	}, nil
}

func (s *StripeGateway) CreateSetupIntent(ctx context.Context, customerID string) (*domain.SetupIntent, error) {
	params := &stripe.SetupIntentParams{
		Customer: stripe.String(customerID),
		Usage:    stripe.String("off_session"),
		PaymentMethodTypes: stripe.StringSlice([]string{
			"card",
		}),
	}

	result, err := setupintent.New(params)
	if err != nil {
		return nil, fmt.Errorf("failed to create setup intent: %w", err)
	}

	return &domain.SetupIntent{
		ID:           result.ID,
		ClientSecret: result.ClientSecret,
		Status:       string(result.Status),
		CustomerID:   customerID,
	}, nil
}

func (s *StripeGateway) AttachPaymentMethodToCustomer(ctx context.Context, paymentMethodID, customerID string) error {
	params := &stripe.PaymentMethodAttachParams{
		Customer: stripe.String(customerID),
	}

	_, err := paymentmethod.Attach(paymentMethodID, params)
	if err != nil {
		return fmt.Errorf("failed to attach payment method to customer: %w", err)
	}

	return nil
}

func (s *StripeGateway) DetachPaymentMethodFromCustomer(ctx context.Context, paymentMethodID string) error {
	_, err := paymentmethod.Detach(paymentMethodID, nil)
	if err != nil {
		return fmt.Errorf("failed to detach payment method: %w", err)
	}

	return nil
}

func (s *StripeGateway) GetPaymentMethod(ctx context.Context, paymentMethodID string) (*PaymentMethodInfo, error) {
	pm, err := paymentmethod.Get(paymentMethodID, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get payment method: %w", err)
	}

	info := &PaymentMethodInfo{
		ID:   pm.ID,
		Type: string(pm.Type),
	}

	if pm.Card != nil {
		info.Brand = stripe.String(string(pm.Card.Brand))
		info.Last4 = stripe.String(pm.Card.Last4)
		expiryMonth := int(pm.Card.ExpMonth)
		expiryYear := int(pm.Card.ExpYear)
		info.ExpiryMonth = &expiryMonth
		info.ExpiryYear = &expiryYear
	}

	return info, nil
}

func (s *StripeGateway) ListCustomerPaymentMethods(ctx context.Context, customerID string) ([]*PaymentMethodInfo, error) {
	params := &stripe.PaymentMethodListParams{
		Customer: stripe.String(customerID),
		Type:     stripe.String("card"),
	}

	iter := paymentmethod.List(params)
	var paymentMethods []*PaymentMethodInfo

	for iter.Next() {
		pm := iter.PaymentMethod()
		info := &PaymentMethodInfo{
			ID:   pm.ID,
			Type: string(pm.Type),
		}

		if pm.Card != nil {
			info.Brand = stripe.String(string(pm.Card.Brand))
			info.Last4 = stripe.String(pm.Card.Last4)
			expiryMonth := int(pm.Card.ExpMonth)
			expiryYear := int(pm.Card.ExpYear)
			info.ExpiryMonth = &expiryMonth
			info.ExpiryYear = &expiryYear
		}

		paymentMethods = append(paymentMethods, info)
	}

	if err := iter.Err(); err != nil {
		return nil, fmt.Errorf("failed to list payment methods: %w", err)
	}

	return paymentMethods, nil
}

func (s *StripeGateway) SetDefaultPaymentMethod(ctx context.Context, customerID, paymentMethodID string) error {
	params := &stripe.CustomerParams{
		InvoiceSettings: &stripe.CustomerInvoiceSettingsParams{
			DefaultPaymentMethod: stripe.String(paymentMethodID),
		},
	}

	_, err := customer.Update(customerID, params)
	if err != nil {
		return fmt.Errorf("failed to set default payment method: %w", err)
	}

	return nil
}

func (s *StripeGateway) CreateOffSessionPayment(ctx context.Context, req *OffSessionPaymentRequest) (*PaymentResult, error) {
	params := &stripe.PaymentIntentParams{
		Amount:             stripe.Int64(req.Amount),
		Currency:           stripe.String(req.Currency),
		Customer:           stripe.String(req.CustomerID),
		PaymentMethod:      stripe.String(req.PaymentMethodID),
		Description:        stripe.String(req.Description),
		ConfirmationMethod: stripe.String("automatic"),
		Confirm:            stripe.Bool(true),
		OffSession:         stripe.Bool(true),
	}

	if req.Metadata != nil {
		params.Metadata = req.Metadata
	}

	result, err := paymentintent.New(params)
	if err != nil {
		return nil, fmt.Errorf("failed to create off-session payment: %w", err)
	}

	paymentResult := &PaymentResult{
		PaymentIntentID: result.ID,
		Status:          string(result.Status),
		Amount:          result.Amount,
		Currency:        string(result.Currency),
	}

	if result.PaymentMethod != nil {
		pm, err := paymentmethod.Get(result.PaymentMethod.ID, nil)
		if err == nil {
			paymentResult.PaymentMethod = &PaymentMethodInfo{
				ID:   pm.ID,
				Type: string(pm.Type),
			}
			if pm.Card != nil {
				paymentResult.PaymentMethod.Brand = stripe.String(string(pm.Card.Brand))
				paymentResult.PaymentMethod.Last4 = stripe.String(pm.Card.Last4)
			}
		}
	}

	if result.LatestCharge != nil && result.LatestCharge.ReceiptURL != "" {
		paymentResult.ReceiptURL = &result.LatestCharge.ReceiptURL
	}

	return paymentResult, nil
}

func (s *StripeGateway) CreatePaymentLink(ctx context.Context, amount int, currency, description string) (*PaymentLinkResult, error) {
	params := &stripe.PaymentLinkParams{
		LineItems: []*stripe.PaymentLinkLineItemParams{
			{
				Price:    stripe.String("price_1234"), // You'll need to create prices in Stripe
				Quantity: stripe.Int64(1),
			},
		},
	}

	result, err := paymentlink.New(params)
	if err != nil {
		return nil, fmt.Errorf("failed to create payment link: %w", err)
	}

	return &PaymentLinkResult{
		ID:  result.ID,
		URL: result.URL,
	}, nil
}

func (s *StripeGateway) CreatePaymentIntent(ctx context.Context, amount int, currency, customerID, paymentMethodID string) (*PaymentIntentInfo, error) {
	params := &stripe.PaymentIntentParams{
		Amount:        stripe.Int64(int64(amount)),
		Currency:      stripe.String(currency),
		Customer:      stripe.String(customerID),
		PaymentMethod: stripe.String(paymentMethodID),
	}

	result, err := paymentintent.New(params)
	if err != nil {
		return nil, fmt.Errorf("failed to create payment intent: %w", err)
	}

	return &PaymentIntentInfo{
		ID:       result.ID,
		Status:   string(result.Status),
		Amount:   result.Amount,
		Currency: string(result.Currency),
	}, nil
}

func (s *StripeGateway) ConfirmPaymentIntent(ctx context.Context, paymentIntentID string) (*PaymentConfirmResult, error) {
	params := &stripe.PaymentIntentConfirmParams{}
	result, err := paymentintent.Confirm(paymentIntentID, params)
	if err != nil {
		return nil, fmt.Errorf("failed to confirm payment intent: %w", err)
	}

	confirmResult := &PaymentConfirmResult{
		ID:     result.ID,
		Status: string(result.Status),
	}

	if result.LatestCharge != nil && result.LatestCharge.ReceiptURL != "" {
		confirmResult.ReceiptURL = result.LatestCharge.ReceiptURL
	}

	return confirmResult, nil
}

func (s *StripeGateway) VerifyWebhook(ctx context.Context, payload []byte, signature string) (*WebhookEvent, error) {
	// Use ConstructEventWithOptions to ignore API version mismatch
	options := webhook.ConstructEventOptions{
		IgnoreAPIVersionMismatch: true,
	}

	event, err := webhook.ConstructEventWithOptions(payload, signature, s.webhookEndpointSecret, options)
	if err != nil {
		return nil, fmt.Errorf("failed to verify webhook signature: %w", err)
	}

	// Parse the event data
	var eventData map[string]interface{}
	if err := json.Unmarshal(event.Data.Raw, &eventData); err != nil {
		return nil, fmt.Errorf("failed to parse webhook event data: %w", err)
	}

	return &WebhookEvent{
		Type: string(event.Type),
		Data: eventData,
	}, nil
}

func (s *StripeGateway) GetPaymentIntent(ctx context.Context, paymentIntentID string) (*PaymentIntentInfo, error) {
	pi, err := paymentintent.Get(paymentIntentID, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get payment intent: %w", err)
	}

	info := &PaymentIntentInfo{
		ID:       pi.ID,
		Status:   string(pi.Status),
		Amount:   pi.Amount,
		Currency: string(pi.Currency),
	}

	if pi.LatestCharge != nil {
		info.ReceiptURL = &pi.ReceiptEmail
	}

	return info, nil
}
