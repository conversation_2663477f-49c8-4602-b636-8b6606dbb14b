package repository

import (
	"context"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/model"
)

type userSessionRepository struct {
	db *gorm.DB
}

func NewUserSessionRepository(db *gorm.DB) UserSessionRepository {
	return &userSessionRepository{db: db}
}

func (r *userSessionRepository) Create(ctx context.Context, session *domain.UserSession) error {
	sessionModel := &model.UserSessionModel{}
	sessionModel.FromDomain(session)

	err := r.db.WithContext(ctx).Create(sessionModel).Error
	if err != nil {
		return err
	}

	*session = *sessionModel.ToDomain()
	return nil
}

func (r *userSessionRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.UserSession, error) {
	var sessionModel model.UserSessionModel
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&sessionModel).Error
	if err != nil {
		return nil, err
	}
	return sessionModel.ToDomain(), nil
}

func (r *userSessionRepository) GetByRefreshTokenHash(ctx context.Context, tokenHash string) (*domain.UserSession, error) {
	var sessionModel model.UserSessionModel
	err := r.db.WithContext(ctx).Where("refresh_token_hash = ? AND expires_at > NOW()", tokenHash).First(&sessionModel).Error
	if err != nil {
		return nil, err
	}
	return sessionModel.ToDomain(), nil
}

func (r *userSessionRepository) GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.UserSession, error) {
	var sessionModels []model.UserSessionModel
	err := r.db.WithContext(ctx).Where("user_id = ? AND expires_at > NOW()", userID).Order("last_used_at DESC").Find(&sessionModels).Error
	if err != nil {
		return nil, err
	}

	sessions := make([]*domain.UserSession, len(sessionModels))
	for i, sessionModel := range sessionModels {
		sessions[i] = sessionModel.ToDomain()
	}
	return sessions, nil
}

func (r *userSessionRepository) GetByUserAndDevice(ctx context.Context, userID uuid.UUID, deviceID string) (*domain.UserSession, error) {
	var sessionModel model.UserSessionModel
	err := r.db.WithContext(ctx).Where("user_id = ? AND device_id = ? AND expires_at > NOW()", userID, deviceID).First(&sessionModel).Error
	if err != nil {
		return nil, err
	}
	return sessionModel.ToDomain(), nil
}

func (r *userSessionRepository) Update(ctx context.Context, session *domain.UserSession) error {
	sessionModel := &model.UserSessionModel{}
	sessionModel.FromDomain(session)

	err := r.db.WithContext(ctx).Save(sessionModel).Error
	if err != nil {
		return err
	}

	*session = *sessionModel.ToDomain()
	return nil
}

func (r *userSessionRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&model.UserSessionModel{}, "id = ?", id).Error
}

func (r *userSessionRepository) DeleteByUserID(ctx context.Context, userID uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&model.UserSessionModel{}, "user_id = ?", userID).Error
}

func (r *userSessionRepository) DeleteByUserAndDevice(ctx context.Context, userID uuid.UUID, deviceID string) error {
	return r.db.WithContext(ctx).Delete(&model.UserSessionModel{}, "user_id = ? AND device_id = ?", userID, deviceID).Error
}

func (r *userSessionRepository) DeleteExpired(ctx context.Context) error {
	return r.db.WithContext(ctx).Delete(&model.UserSessionModel{}, "expires_at <= NOW()").Error
}

func (r *userSessionRepository) CleanupExpiredSessions(ctx context.Context) (int, error) {
	result := r.db.WithContext(ctx).Delete(&model.UserSessionModel{}, "expires_at <= NOW()")
	if result.Error != nil {
		return 0, result.Error
	}
	return int(result.RowsAffected), nil
}

func (r *userSessionRepository) CreateOrUpdateSessionWithLogin(ctx context.Context, userID uuid.UUID, deviceID string, session *domain.UserSession, user *domain.User) (*domain.UserSession, error) {
	var resultSession *domain.UserSession

	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var existingSessionModel model.UserSessionModel
		err := tx.Where("user_id = ? AND device_id = ? AND expires_at > NOW()", userID, deviceID).First(&existingSessionModel).Error

		if err == nil {
			existingSession := existingSessionModel.ToDomain()
			existingSession.UpdateLastUsed()
			existingSession.UpdateDeviceInfo(session.DeviceName, session.DeviceType, session.UserAgent, session.IPAddress)
			existingSession.RefreshTokenHash = session.RefreshTokenHash
			existingSession.ExpiresAt = session.ExpiresAt
			existingSession.UpdatedAt = session.UpdatedAt

			existingSessionModel.FromDomain(existingSession)
			if err := tx.Save(&existingSessionModel).Error; err != nil {
				return err
			}
			resultSession = existingSessionModel.ToDomain()
		} else {
			sessionModel := &model.UserSessionModel{}
			sessionModel.FromDomain(session)
			if err := tx.Create(sessionModel).Error; err != nil {
				return err
			}
			resultSession = sessionModel.ToDomain()
		}

		user.RecordLogin()
		userModel := &model.UserModel{}
		userModel.FromDomain(user)
		if err := tx.Save(userModel).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return resultSession, nil
}
