package repository

import (
	"context"
	"errors"
	"fmt"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/model"
)

type paymentRepository struct {
	db *gorm.DB
}

func NewPaymentRepository(db *gorm.DB) PaymentRepository {
	return &paymentRepository{
		db: db,
	}
}

func (r *paymentRepository) Create(ctx context.Context, payment *domain.Payment) error {
	model := &model.PaymentModel{}
	model.FromDomain(payment)

	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
		return fmt.Errorf("failed to create payment: %w", err)
	}

	*payment = *model.ToDomain()
	return nil
}

func (r *paymentRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.Payment, error) {
	var model model.PaymentModel
	if err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("payment not found")
		}
		return nil, fmt.Errorf("failed to get payment: %w", err)
	}

	return model.ToDomain(), nil
}

func (r *paymentRepository) GetBySessionID(ctx context.Context, sessionID uuid.UUID) (*domain.Payment, error) {
	var model model.PaymentModel
	if err := r.db.WithContext(ctx).Where("session_id = ? AND deleted_at IS NULL", sessionID).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("payment not found")
		}
		return nil, fmt.Errorf("failed to get payment by session ID: %w", err)
	}

	return model.ToDomain(), nil
}

func (r *paymentRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Payment, error) {
	var models []model.PaymentModel
	query := r.db.WithContext(ctx).Where("user_id = ? AND deleted_at IS NULL", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset)

	if err := query.Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to get payments by user ID: %w", err)
	}

	payments := make([]*domain.Payment, len(models))
	for i, model := range models {
		payments[i] = model.ToDomain()
	}

	return payments, nil
}

func (r *paymentRepository) GetByStripePaymentIntentID(ctx context.Context, stripePaymentIntentID string) (*domain.Payment, error) {
	var model model.PaymentModel
	if err := r.db.WithContext(ctx).Where("stripe_payment_intent_id = ? AND deleted_at IS NULL", stripePaymentIntentID).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("payment not found")
		}
		return nil, fmt.Errorf("failed to get payment by Stripe payment intent ID: %w", err)
	}

	return model.ToDomain(), nil
}

func (r *paymentRepository) Update(ctx context.Context, payment *domain.Payment) error {
	model := &model.PaymentModel{}
	model.FromDomain(payment)

	if err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", payment.ID).Updates(model).Error; err != nil {
		return fmt.Errorf("failed to update payment: %w", err)
	}

	return nil
}

func (r *paymentRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Model(&model.PaymentModel{}).
		Where("id = ? AND deleted_at IS NULL", id).
		Update("deleted_at", gorm.Expr("NOW()")).Error; err != nil {
		return fmt.Errorf("failed to delete payment: %w", err)
	}

	return nil
}
