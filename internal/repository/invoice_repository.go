package repository

import (
	"context"
	"errors"
	"fmt"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/model"
)

type invoiceRepository struct {
	db *gorm.DB
}

func NewInvoiceRepository(db *gorm.DB) InvoiceRepository {
	return &invoiceRepository{
		db: db,
	}
}

func (r *invoiceRepository) Create(ctx context.Context, invoice *domain.Invoice) error {
	model := &model.InvoiceModel{}
	model.FromDomain(invoice)

	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
		return fmt.Errorf("failed to create invoice: %w", err)
	}

	*invoice = *model.ToDomain()
	return nil
}

func (r *invoiceRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.Invoice, error) {
	var model model.InvoiceModel
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("invoice not found")
		}
		return nil, fmt.Errorf("failed to get invoice by ID: %w", err)
	}

	return model.ToDomain(), nil
}

func (r *invoiceRepository) GetByStripeInvoiceID(ctx context.Context, stripeInvoiceID string) (*domain.Invoice, error) {
	var model model.InvoiceModel
	if err := r.db.WithContext(ctx).Where("stripe_invoice_id = ?", stripeInvoiceID).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("invoice not found")
		}
		return nil, fmt.Errorf("failed to get invoice by Stripe invoice ID: %w", err)
	}

	return model.ToDomain(), nil
}

func (r *invoiceRepository) GetByStripeCustomerID(ctx context.Context, stripeCustomerID string, limit, offset int) ([]*domain.Invoice, error) {
	var models []model.InvoiceModel
	if err := r.db.WithContext(ctx).Where("stripe_customer_id = ?", stripeCustomerID).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to get invoices by Stripe customer ID: %w", err)
	}

	invoices := make([]*domain.Invoice, len(models))
	for i, model := range models {
		invoices[i] = model.ToDomain()
	}

	return invoices, nil
}

func (r *invoiceRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Invoice, error) {
	var models []model.InvoiceModel
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to get invoices by user ID: %w", err)
	}

	invoices := make([]*domain.Invoice, len(models))
	for i, model := range models {
		invoices[i] = model.ToDomain()
	}

	return invoices, nil
}

func (r *invoiceRepository) Update(ctx context.Context, invoice *domain.Invoice) error {
	model := &model.InvoiceModel{}
	model.FromDomain(invoice)

	if err := r.db.WithContext(ctx).Where("id = ?", invoice.ID).Updates(model).Error; err != nil {
		return fmt.Errorf("failed to update invoice: %w", err)
	}

	return nil
}

func (r *invoiceRepository) List(ctx context.Context, limit, offset int) ([]*domain.Invoice, error) {
	var models []model.InvoiceModel
	if err := r.db.WithContext(ctx).Limit(limit).Offset(offset).Order("created_at DESC").Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to list invoices: %w", err)
	}

	invoices := make([]*domain.Invoice, len(models))
	for i, model := range models {
		invoices[i] = model.ToDomain()
	}

	return invoices, nil
}

func (r *invoiceRepository) ListByStatus(ctx context.Context, status domain.InvoiceStatus, limit, offset int) ([]*domain.Invoice, error) {
	var models []model.InvoiceModel
	if err := r.db.WithContext(ctx).Where("status = ?", string(status)).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to list invoices by status: %w", err)
	}

	invoices := make([]*domain.Invoice, len(models))
	for i, model := range models {
		invoices[i] = model.ToDomain()
	}

	return invoices, nil
}
