package repository

import (
	"context"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/model"
)

type verificationTokenRepository struct {
	db *gorm.DB
}

func NewVerificationTokenRepository(db *gorm.DB) VerificationTokenRepository {
	return &verificationTokenRepository{db: db}
}

func (r *verificationTokenRepository) Create(ctx context.Context, token *domain.VerificationToken) error {
	tokenModel := &model.VerificationTokenModel{}
	tokenModel.FromDomain(token)

	err := r.db.WithContext(ctx).Create(tokenModel).Error
	if err != nil {
		return err
	}

	*token = *tokenModel.ToDomain()
	return nil
}

func (r *verificationTokenRepository) GetByTokenHash(ctx context.Context, tokenHash string) (*domain.VerificationToken, error) {
	var tokenModel model.VerificationTokenModel
	err := r.db.WithContext(ctx).Where("token_hash = ? AND used_at IS NULL AND expires_at > NOW()", tokenHash).First(&tokenModel).Error
	if err != nil {
		return nil, err
	}
	return tokenModel.ToDomain(), nil
}

func (r *verificationTokenRepository) GetByUserIDAndType(ctx context.Context, userID uuid.UUID, tokenType domain.VerificationType) (*domain.VerificationToken, error) {
	var tokenModel model.VerificationTokenModel
	err := r.db.WithContext(ctx).Where("user_id = ? AND token_type = ? AND used_at IS NULL AND expires_at > NOW()", userID, tokenType).First(&tokenModel).Error
	if err != nil {
		return nil, err
	}
	return tokenModel.ToDomain(), nil
}

func (r *verificationTokenRepository) Update(ctx context.Context, token *domain.VerificationToken) error {
	tokenModel := &model.VerificationTokenModel{}
	tokenModel.FromDomain(token)

	err := r.db.WithContext(ctx).Save(tokenModel).Error
	if err != nil {
		return err
	}

	*token = *tokenModel.ToDomain()
	return nil
}

func (r *verificationTokenRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&model.VerificationTokenModel{}, "id = ?", id).Error
}

func (r *verificationTokenRepository) DeleteByUserIDAndType(ctx context.Context, userID uuid.UUID, tokenType domain.VerificationType) error {
	return r.db.WithContext(ctx).Delete(&model.VerificationTokenModel{}, "user_id = ? AND token_type = ?", userID, tokenType).Error
}

func (r *verificationTokenRepository) DeleteExpired(ctx context.Context) error {
	return r.db.WithContext(ctx).Delete(&model.VerificationTokenModel{}, "expires_at <= NOW()").Error
}

func (r *verificationTokenRepository) CleanupExpiredTokens(ctx context.Context) (int, error) {
	result := r.db.WithContext(ctx).Delete(&model.VerificationTokenModel{}, "expires_at <= NOW()")
	if result.Error != nil {
		return 0, result.Error
	}
	return int(result.RowsAffected), nil
}

func (r *verificationTokenRepository) ReplaceVerificationToken(ctx context.Context, userID uuid.UUID, tokenType domain.VerificationType, newToken *domain.VerificationToken) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Delete(&model.VerificationTokenModel{}, "user_id = ? AND token_type = ?", userID, tokenType).Error; err != nil {
			return err
		}

		tokenModel := &model.VerificationTokenModel{}
		tokenModel.FromDomain(newToken)
		if err := tx.Create(tokenModel).Error; err != nil {
			return err
		}
		*newToken = *tokenModel.ToDomain()

		return nil
	})
}
