package repository

import (
	"context"
	"errors"
	"fmt"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/model"
)

type paymentMethodRepository struct {
	db *gorm.DB
}

func NewPaymentMethodRepository(db *gorm.DB) PaymentMethodRepository {
	return &paymentMethodRepository{
		db: db,
	}
}

func (r *paymentMethodRepository) Create(ctx context.Context, paymentMethod *domain.PaymentMethod) error {
	model := &model.PaymentMethodModel{}
	model.FromDomain(paymentMethod)

	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
		return fmt.Errorf("failed to create payment method: %w", err)
	}

	*paymentMethod = *model.ToDomain()
	return nil
}

func (r *paymentMethodRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.PaymentMethod, error) {
	var model model.PaymentMethodModel
	if err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrPaymentMethodNotFound
		}
		return nil, fmt.Errorf("failed to get payment method by ID: %w", err)
	}

	return model.ToDomain(), nil
}

func (r *paymentMethodRepository) GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.PaymentMethod, error) {
	var models []model.PaymentMethodModel
	if err := r.db.WithContext(ctx).Where("user_id = ? AND deleted_at IS NULL", userID).
		Order("is_default DESC, created_at DESC").Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to get payment methods by user ID: %w", err)
	}

	paymentMethods := make([]*domain.PaymentMethod, len(models))
	for i, model := range models {
		paymentMethods[i] = model.ToDomain()
	}

	return paymentMethods, nil
}

func (r *paymentMethodRepository) GetByStripePaymentMethodID(ctx context.Context, stripePaymentMethodID string) (*domain.PaymentMethod, error) {
	var model model.PaymentMethodModel
	if err := r.db.WithContext(ctx).Where("stripe_payment_method_id = ? AND deleted_at IS NULL", stripePaymentMethodID).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrPaymentMethodNotFound
		}
		return nil, fmt.Errorf("failed to get payment method by Stripe ID: %w", err)
	}

	return model.ToDomain(), nil
}

func (r *paymentMethodRepository) GetDefaultByUserID(ctx context.Context, userID uuid.UUID) (*domain.PaymentMethod, error) {
	var model model.PaymentMethodModel
	if err := r.db.WithContext(ctx).Where("user_id = ? AND is_default = true AND deleted_at IS NULL", userID).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrPaymentMethodNotFound
		}
		return nil, fmt.Errorf("failed to get default payment method: %w", err)
	}

	return model.ToDomain(), nil
}

func (r *paymentMethodRepository) Update(ctx context.Context, paymentMethod *domain.PaymentMethod) error {
	model := &model.PaymentMethodModel{}
	model.FromDomain(paymentMethod)

	if err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", paymentMethod.ID).Updates(model).Error; err != nil {
		return fmt.Errorf("failed to update payment method: %w", err)
	}

	return nil
}

func (r *paymentMethodRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Model(&model.PaymentMethodModel{}).
		Where("id = ? AND deleted_at IS NULL", id).
		Update("deleted_at", gorm.Expr("NOW()")).Error; err != nil {
		return fmt.Errorf("failed to delete payment method: %w", err)
	}

	return nil
}

func (r *paymentMethodRepository) SetAsDefault(ctx context.Context, id uuid.UUID, userID uuid.UUID) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&model.PaymentMethodModel{}).
			Where("user_id = ? AND deleted_at IS NULL", userID).
			Update("is_default", false).Error; err != nil {
			return fmt.Errorf("failed to unset default payment methods: %w", err)
		}

		if err := tx.Model(&model.PaymentMethodModel{}).
			Where("id = ? AND user_id = ? AND deleted_at IS NULL", id, userID).
			Update("is_default", true).Error; err != nil {
			return fmt.Errorf("failed to set payment method as default: %w", err)
		}

		return nil
	})
}

func (r *paymentMethodRepository) UnsetDefaultForUser(ctx context.Context, userID uuid.UUID) error {
	if err := r.db.WithContext(ctx).Model(&model.PaymentMethodModel{}).
		Where("user_id = ? AND deleted_at IS NULL", userID).
		Update("is_default", false).Error; err != nil {
		return fmt.Errorf("failed to unset default payment methods: %w", err)
	}

	return nil
}
