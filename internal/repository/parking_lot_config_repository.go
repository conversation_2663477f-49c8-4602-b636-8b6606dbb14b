package repository

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/model"
)

type parkingLotConfigRepository struct {
	db *gorm.DB
}

func NewParkingLotConfigRepository(db *gorm.DB) ParkingLotConfigRepository {
	return &parkingLotConfigRepository{db: db}
}

func (r *parkingLotConfigRepository) Create(ctx context.Context, config *domain.ParkingLotConfig) error {
	configModel := &model.ParkingLotConfigModel{}
	configModel.FromDomain(config)
	return r.db.WithContext(ctx).Create(configModel).Error
}

func (r *parkingLotConfigRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.ParkingLotConfig, error) {
	var configModel model.ParkingLotConfigModel
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&configModel).Error
	if err != nil {
		return nil, err
	}
	return configModel.ToDomain(), nil
}

func (r *parkingLotConfigRepository) GetByParkingLotID(ctx context.Context, parkingLotID uuid.UUID, includeInactive bool) ([]*domain.ParkingLotConfig, error) {
	var models []model.ParkingLotConfigModel
	query := r.db.WithContext(ctx).Where("parking_lot_id = ? AND deleted_at IS NULL", parkingLotID)

	if !includeInactive {
		query = query.Where("is_active = ?", true)
	}

	err := query.Order("created_at DESC").Find(&models).Error
	if err != nil {
		return nil, err
	}

	configs := make([]*domain.ParkingLotConfig, len(models))
	for i, configModel := range models {
		configs[i] = configModel.ToDomain()
	}
	return configs, nil
}

func (r *parkingLotConfigRepository) GetActiveByParkingLotID(ctx context.Context, parkingLotID uuid.UUID, effectiveAt time.Time) (*domain.ParkingLotConfig, error) {
	var configModel model.ParkingLotConfigModel
	query := r.db.WithContext(ctx).Where(`
		parking_lot_id = ? AND 
		is_active = true AND 
		deleted_at IS NULL AND
		effective_from <= ? AND
		(effective_until IS NULL OR effective_until >= ?)
	`, parkingLotID, effectiveAt, effectiveAt)

	err := query.Order("effective_from DESC").First(&configModel).Error
	if err != nil {
		return nil, err
	}
	return configModel.ToDomain(), nil
}

func (r *parkingLotConfigRepository) Update(ctx context.Context, config *domain.ParkingLotConfig) error {
	configModel := &model.ParkingLotConfigModel{}
	configModel.FromDomain(config)
	return r.db.WithContext(ctx).Save(configModel).Error
}

func (r *parkingLotConfigRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&model.ParkingLotConfigModel{}).
		Where("id = ?", id).
		Update("deleted_at", time.Now()).Error
}

func (r *parkingLotConfigRepository) DeactivateAllForParkingLot(ctx context.Context, parkingLotID uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&model.ParkingLotConfigModel{}).
		Where("parking_lot_id = ? AND is_active = true", parkingLotID).
		Update("is_active", false).Error
}
