package repository

import (
	"context"
	"errors"
	"fmt"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/model"
)

type disputeRepository struct {
	db *gorm.DB
}

func NewDisputeRepository(db *gorm.DB) DisputeRepository {
	return &disputeRepository{
		db: db,
	}
}

func (r *disputeRepository) Create(ctx context.Context, dispute *domain.Dispute) error {
	model := &model.DisputeModel{}
	model.FromDomain(dispute)

	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
		return fmt.Errorf("failed to create dispute: %w", err)
	}

	*dispute = *model.ToDomain()
	return nil
}

func (r *disputeRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.Dispute, error) {
	var model model.DisputeModel
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("dispute not found")
		}
		return nil, fmt.Errorf("failed to get dispute by ID: %w", err)
	}

	return model.ToDomain(), nil
}

func (r *disputeRepository) GetByStripeDisputeID(ctx context.Context, stripeDisputeID string) (*domain.Dispute, error) {
	var model model.DisputeModel
	if err := r.db.WithContext(ctx).Where("stripe_dispute_id = ?", stripeDisputeID).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("dispute not found")
		}
		return nil, fmt.Errorf("failed to get dispute by Stripe dispute ID: %w", err)
	}

	return model.ToDomain(), nil
}

func (r *disputeRepository) GetByStripeChargeID(ctx context.Context, stripeChargeID string) (*domain.Dispute, error) {
	var model model.DisputeModel
	if err := r.db.WithContext(ctx).Where("stripe_charge_id = ?", stripeChargeID).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("dispute not found")
		}
		return nil, fmt.Errorf("failed to get dispute by Stripe charge ID: %w", err)
	}

	return model.ToDomain(), nil
}

func (r *disputeRepository) GetByPaymentID(ctx context.Context, paymentID uuid.UUID) ([]*domain.Dispute, error) {
	var models []model.DisputeModel
	if err := r.db.WithContext(ctx).Where("payment_id = ?", paymentID).Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to get disputes by payment ID: %w", err)
	}

	disputes := make([]*domain.Dispute, len(models))
	for i, model := range models {
		disputes[i] = model.ToDomain()
	}

	return disputes, nil
}

func (r *disputeRepository) Update(ctx context.Context, dispute *domain.Dispute) error {
	model := &model.DisputeModel{}
	model.FromDomain(dispute)

	if err := r.db.WithContext(ctx).Where("id = ?", dispute.ID).Updates(model).Error; err != nil {
		return fmt.Errorf("failed to update dispute: %w", err)
	}

	return nil
}

func (r *disputeRepository) List(ctx context.Context, limit, offset int) ([]*domain.Dispute, error) {
	var models []model.DisputeModel
	if err := r.db.WithContext(ctx).Limit(limit).Offset(offset).Order("created_at DESC").Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to list disputes: %w", err)
	}

	disputes := make([]*domain.Dispute, len(models))
	for i, model := range models {
		disputes[i] = model.ToDomain()
	}

	return disputes, nil
}

func (r *disputeRepository) ListByStatus(ctx context.Context, status domain.DisputeStatus, limit, offset int) ([]*domain.Dispute, error) {
	var models []model.DisputeModel
	if err := r.db.WithContext(ctx).Where("status = ?", string(status)).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to list disputes by status: %w", err)
	}

	disputes := make([]*domain.Dispute, len(models))
	for i, model := range models {
		disputes[i] = model.ToDomain()
	}

	return disputes, nil
}
