package repository

import (
	"context"
	"errors"
	"fmt"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/model"
)

type sessionRepository struct {
	db *gorm.DB
}

func NewSessionRepository(db *gorm.DB) SessionRepository {
	return &sessionRepository{
		db: db,
	}
}

func (r *sessionRepository) Create(ctx context.Context, session *domain.Session) error {
	model := &model.SessionModel{}
	model.FromDomain(session)

	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
		return fmt.Errorf("failed to create session: %w", err)
	}

	*session = *model.ToDomain()
	return nil
}

func (r *sessionRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.Session, error) {
	var model model.SessionModel
	if err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("session not found")
		}
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	return model.ToDomain(), nil
}

func (r *sessionRepository) GetActiveByPlateID(ctx context.Context, plateID uuid.UUID) (*domain.Session, error) {
	var model model.SessionModel
	if err := r.db.WithContext(ctx).Where("plate_id = ? AND status = ? AND deleted_at IS NULL", plateID, "active").First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("active session not found")
		}
		return nil, fmt.Errorf("failed to get active session: %w", err)
	}

	return model.ToDomain(), nil
}

func (r *sessionRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Session, error) {
	var models []model.SessionModel
	query := r.db.WithContext(ctx).Where("user_id = ? AND deleted_at IS NULL", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset)

	if err := query.Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to get sessions by user ID: %w", err)
	}

	sessions := make([]*domain.Session, len(models))
	for i, model := range models {
		sessions[i] = model.ToDomain()
	}

	return sessions, nil
}

func (r *sessionRepository) Update(ctx context.Context, session *domain.Session) error {
	model := &model.SessionModel{}
	model.FromDomain(session)

	if err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", session.ID).Updates(model).Error; err != nil {
		return fmt.Errorf("failed to update session: %w", err)
	}

	return nil
}

func (r *sessionRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Model(&model.SessionModel{}).
		Where("id = ? AND deleted_at IS NULL", id).
		Update("deleted_at", gorm.Expr("NOW()")).Error; err != nil {
		return fmt.Errorf("failed to delete session: %w", err)
	}

	return nil
}
