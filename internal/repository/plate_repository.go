package repository

import (
	"context"
	"errors"
	"fmt"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/model"
)

type plateRepository struct {
	db *gorm.DB
}

func NewPlateRepository(db *gorm.DB) PlateRepository {
	return &plateRepository{db: db}
}

func (r *plateRepository) Create(ctx context.Context, plate *domain.Plate) error {
	if plate == nil {
		return errors.New("plate cannot be nil")
	}

	plateModel := &model.PlateModel{}
	plateModel.FromDomain(plate)

	if err := r.db.WithContext(ctx).Create(plateModel).Error; err != nil {
		if errors.Is(err, gorm.ErrDuplicatedKey) {
			return fmt.Errorf("plate number already exists: %w", err)
		}
		return fmt.Errorf("failed to create plate: %w", err)
	}

	*plate = *plateModel.ToDomain()
	return nil
}

func (r *plateRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.Plate, error) {
	if id == uuid.Nil {
		return nil, errors.New("plate ID cannot be nil")
	}

	var plateModel model.PlateModel
	if err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&plateModel).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("plate not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get plate by ID: %w", err)
	}

	return plateModel.ToDomain(), nil
}

func (r *plateRepository) GetByPlateNumber(ctx context.Context, plateNumber string) (*domain.Plate, error) {
	if plateNumber == "" {
		return nil, errors.New("plate number cannot be empty")
	}

	var plateModel model.PlateModel
	if err := r.db.WithContext(ctx).Where("plate_number = ? AND deleted_at IS NULL", plateNumber).First(&plateModel).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("plate not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get plate by plate number: %w", err)
	}

	return plateModel.ToDomain(), nil
}

func (r *plateRepository) GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.Plate, error) {
	if userID == uuid.Nil {
		return nil, errors.New("user ID cannot be nil")
	}

	var plateModels []model.PlateModel
	if err := r.db.WithContext(ctx).Where("user_id = ? AND deleted_at IS NULL", userID).Order("created_at DESC").Find(&plateModels).Error; err != nil {
		return nil, fmt.Errorf("failed to get plates by user ID: %w", err)
	}

	plates := make([]*domain.Plate, len(plateModels))
	for i, plateModel := range plateModels {
		plates[i] = plateModel.ToDomain()
	}

	return plates, nil
}

func (r *plateRepository) Update(ctx context.Context, plate *domain.Plate) error {
	if plate == nil {
		return errors.New("plate cannot be nil")
	}

	plateModel := &model.PlateModel{}
	plateModel.FromDomain(plate)

	result := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", plate.ID).Updates(plateModel)
	if result.Error != nil {
		return fmt.Errorf("failed to update plate: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return errors.New("plate not found or already deleted")
	}

	return nil
}

func (r *plateRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if id == uuid.Nil {
		return errors.New("plate ID cannot be nil")
	}

	result := r.db.WithContext(ctx).Model(&model.PlateModel{}).
		Where("id = ? AND deleted_at IS NULL", id).
		Update("deleted_at", "NOW()")
	if result.Error != nil {
		return fmt.Errorf("failed to delete plate: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return errors.New("plate not found or already deleted")
	}

	return nil
}

func (r *plateRepository) ExistsByPlateNumber(ctx context.Context, plateNumber string) (bool, error) {
	if plateNumber == "" {
		return false, errors.New("plate number cannot be empty")
	}

	var count int64
	if err := r.db.WithContext(ctx).Model(&model.PlateModel{}).Where("plate_number = ? AND deleted_at IS NULL", plateNumber).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check plate number existence: %w", err)
	}

	return count > 0, nil
}

func (r *plateRepository) ExistsByPlateNumberForUser(ctx context.Context, plateNumber string, userID uuid.UUID) (bool, error) {
	if plateNumber == "" {
		return false, errors.New("plate number cannot be empty")
	}
	if userID == uuid.Nil {
		return false, errors.New("user ID cannot be nil")
	}

	var count int64
	if err := r.db.WithContext(ctx).Model(&model.PlateModel{}).Where("plate_number = ? AND user_id = ? AND deleted_at IS NULL", plateNumber, userID).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check plate number existence for user: %w", err)
	}

	return count > 0, nil
}
