package container

import (
	"context"

	"gorm.io/gorm"

	"github.com/smooth-inc/backend/configs"
	"github.com/smooth-inc/backend/internal/controller"
	"github.com/smooth-inc/backend/internal/gateway/stripe"
	"github.com/smooth-inc/backend/internal/infra"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/repository"
	"github.com/smooth-inc/backend/internal/usecase"
)

type Container struct {
	config       *configs.Config
	logger       *logger.Logger
	db           *gorm.DB
	services     *Services
	gateways     *Gateways
	usecases     *Usecases
	repositories *Repositories
	controller   *controller.Controller
}

type Services struct {
	JWT   usecase.JWTService
	Email usecase.EmailTemplateService
}

type Gateways struct {
	Stripe stripe.Gateway
}

type Repositories struct {
	User              repository.UserRepository
	UserSession       repository.UserSessionRepository
	VerificationToken repository.VerificationTokenRepository
	Plate             repository.PlateRepository
	ParkingLot        repository.ParkingLotRepository
	Session           repository.SessionRepository
	Payment           repository.PaymentRepository
	PaymentMethod     repository.PaymentMethodRepository
	Booking           repository.BookingRepository
	Notification      repository.NotificationRepository
	Dispute           repository.DisputeRepository
	Invoice           repository.InvoiceRepository
}

type Usecases struct {
	Auth          usecase.AuthUsecase
	User          usecase.UserUsecase
	Plate         usecase.PlateUsecase
	ParkingLot    usecase.ParkingLotUsecase
	Session       usecase.SessionUsecase
	Payment       usecase.PaymentUsecase
	PaymentMethod usecase.PaymentMethodUsecase
	Webhook       usecase.WebhookUsecase
	Booking       usecase.BookingUsecase
	Notification  usecase.NotificationUsecase
	Hardware      usecase.HardwareUsecase
}

func New(config *configs.Config, logger *logger.Logger, db *gorm.DB) *Container {
	container := &Container{
		config: config,
		logger: logger,
		db:     db,
	}

	container.initServices()
	container.initGateways()
	container.initRepositories()
	container.initUsecases()
	container.initController()

	return container
}

func (c *Container) initServices() {
	c.services = &Services{
		JWT: infra.NewJWTService(
			c.config.JWT.SecretKey,
			c.config.JWT.ExpiryDuration,
			c.config.JWT.RefreshDuration,
			c.config.JWT.Issuer,
		),
		Email: infra.NewEmailService(
			&c.config.Email,
			"http://localhost:8080/api/v1",
		),
	}
}

func (c *Container) initGateways() {
	c.gateways = &Gateways{
		Stripe: stripe.NewStripeGateway(c.config.Stripe.SecretKey, c.config.Stripe.WebhookSecret),
	}
}

func (c *Container) initRepositories() {
	c.repositories = &Repositories{
		User:              repository.NewUserRepository(c.db),
		UserSession:       repository.NewUserSessionRepository(c.db),
		VerificationToken: repository.NewVerificationTokenRepository(c.db),
		Plate:             repository.NewPlateRepository(c.db),
		ParkingLot:        repository.NewParkingLotRepository(c.db),
		Session:           repository.NewSessionRepository(c.db),
		Payment:           repository.NewPaymentRepository(c.db),
		PaymentMethod:     repository.NewPaymentMethodRepository(c.db),
		Booking:           repository.NewBookingRepository(c.db),
		Notification:      repository.NewNotificationRepository(c.db),
		Dispute:           repository.NewDisputeRepository(c.db),
		Invoice:           repository.NewInvoiceRepository(c.db),
	}
}

func (c *Container) initUsecases() {
	// Create PaymentMethod usecase
	paymentMethodUsecase := usecase.NewPaymentMethodUsecase(
		c.repositories.PaymentMethod,
		c.repositories.User,
		c.gateways.Stripe,
		c.services.Email,
	)

	// Create Payment usecase
	paymentUsecase := usecase.NewPaymentUsecase(
		c.repositories.Payment,
		c.repositories.Session,
		c.repositories.User,
		c.repositories.PaymentMethod,
		c.repositories.Dispute,
		c.repositories.Invoice,
		c.repositories.Notification,
		c.gateways.Stripe,
		c.services.Email,
	)

	webhookUsecase := usecase.NewWebhookUsecase(
		c.gateways.Stripe,
		paymentUsecase,
		paymentMethodUsecase,
	)

	c.usecases = &Usecases{
		Auth: usecase.NewAuthUsecase(
			c.repositories.User,
			c.repositories.UserSession,
			c.repositories.VerificationToken,
			c.services.JWT,
			c.services.Email,
		),
		User: usecase.NewUserUsecase(
			c.repositories.User,
			c.repositories.VerificationToken,
			c.services.Email,
		),
		Plate:         usecase.NewPlateUsecase(c.repositories.Plate),
		Payment:       paymentUsecase,
		PaymentMethod: paymentMethodUsecase,
		Webhook:       webhookUsecase,
		ParkingLot:    usecase.NewParkingLotUsecaseStub(),
		Session: usecase.NewSessionUsecase(
			c.repositories.Session,
			c.repositories.User,
			c.repositories.Plate,
			c.repositories.ParkingLot,
			paymentUsecase,
			c.repositories.Notification,
		),
		Booking: usecase.NewBookingUsecaseStub(),
		Notification: usecase.NewNotificationUsecase(
			c.repositories.Notification,
			c.repositories.User,
			c.services.Email,
		),
		Hardware: usecase.NewHardwareUsecaseStub(),
	}
}

func (c *Container) initController() {
	c.controller = controller.NewController(
		c.usecases.Auth,
		c.usecases.User,
		c.usecases.Plate,
		c.usecases.ParkingLot,
		c.usecases.Session,
		c.usecases.Payment,
		c.usecases.PaymentMethod,
		c.usecases.Webhook,
		c.usecases.Booking,
		c.usecases.Notification,
		c.usecases.Hardware,
		c.logger,
	)
}

func (c *Container) GetController() *controller.Controller {
	return c.controller
}

func (c *Container) GetLogger() *logger.Logger {
	return c.logger
}

func (c *Container) GetJWTService() usecase.JWTService {
	return c.services.JWT
}

func (c *Container) GetWebhookUsecase() usecase.WebhookUsecase {
	return c.usecases.Webhook
}

func (c *Container) Shutdown(ctx context.Context) error {
	if c.db != nil {
		sqlDB, err := c.db.DB()
		if err == nil {
			return sqlDB.Close()
		}
	}
	return nil
}
