package schema

import (
	"context"
	"fmt"

	"gorm.io/gorm"
)

type Manager struct {
	db *gorm.DB
}

func NewManager(db *gorm.DB) *Manager {
	return &Manager{db: db}
}

func (m *Manager) SetupDatabase(ctx context.Context) error {
	if err := EnableExtensions(m.db); err != nil {
		return fmt.Errorf("failed to enable extensions: %w", err)
	}

	if err := CreateEnums(m.db); err != nil {
		return fmt.Errorf("failed to create enums: %w", err)
	}

	if err := AutoMigrateModels(ctx, m.db); err != nil {
		return fmt.Errorf("failed to auto-migrate models: %w", err)
	}

	if err := RunMigrations(m.db); err != nil {
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	return nil
}
