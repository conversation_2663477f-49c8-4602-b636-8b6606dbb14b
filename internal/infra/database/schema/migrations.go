package schema

import (
	"fmt"

	"gorm.io/gorm"
)

func RunMigrations(db *gorm.DB) error {
	if err := removeTokenColumnsFromUsers(db); err != nil {
		return fmt.Errorf("failed to remove token columns from users: %w", err)
	}

	if err := addUniqueConstraints(db); err != nil {
		return fmt.Errorf("failed to add unique constraints: %w", err)
	}

	if err := addPlateConstraints(db); err != nil {
		return fmt.Errorf("failed to add plate constraints: %w", err)
	}

	return nil
}

func removeTokenColumnsFromUsers(db *gorm.DB) error {
	migrator := db.Migrator()

	if migrator.HasColumn("users", "email_verification_token") {
		if err := migrator.DropColumn("users", "email_verification_token"); err != nil {
			return fmt.E<PERSON>rf("failed to drop email_verification_token column: %w", err)
		}
	}

	if migrator.HasColumn("users", "email_verification_expiry") {
		if err := migrator.DropColumn("users", "email_verification_expiry"); err != nil {
			return fmt.Errorf("failed to drop email_verification_expiry column: %w", err)
		}
	}

	if migrator.HasColumn("users", "password_reset_token") {
		if err := migrator.DropColumn("users", "password_reset_token"); err != nil {
			return fmt.Errorf("failed to drop password_reset_token column: %w", err)
		}
	}

	if migrator.HasColumn("users", "password_reset_expiry") {
		if err := migrator.DropColumn("users", "password_reset_expiry"); err != nil {
			return fmt.Errorf("failed to drop password_reset_expiry column: %w", err)
		}
	}

	return nil
}

func addUniqueConstraints(db *gorm.DB) error {
	// Add unique constraint for user_sessions (user_id, device_id)
	if err := db.Exec(`
		DO $$ BEGIN
			IF NOT EXISTS (
				SELECT 1 FROM pg_constraint 
				WHERE conname = 'idx_user_sessions_user_device_unique'
			) THEN
				ALTER TABLE user_sessions 
				ADD CONSTRAINT idx_user_sessions_user_device_unique 
				UNIQUE (user_id, device_id);
			END IF;
		END $$;
	`).Error; err != nil {
		return fmt.Errorf("failed to add unique constraint for user_sessions: %w", err)
	}

	if err := db.Exec(`
		DO $$ BEGIN
			IF NOT EXISTS (
				SELECT 1 FROM pg_indexes 
				WHERE indexname = 'idx_verification_tokens_user_type_active'
			) THEN
				CREATE UNIQUE INDEX idx_verification_tokens_user_type_active 
				ON verification_tokens(user_id, token_type) 
				WHERE used_at IS NULL;
			END IF;
		END $$;
	`).Error; err != nil {
		return fmt.Errorf("failed to add unique constraint for verification_tokens: %w", err)
	}

	return nil
}

func addPlateConstraints(db *gorm.DB) error {
	if err := db.Exec(`
		DO $$ BEGIN
			IF NOT EXISTS (
				SELECT 1 FROM pg_indexes
				WHERE indexname = 'idx_plates_plate_number_unique'
			) THEN
				CREATE UNIQUE INDEX idx_plates_plate_number_unique
				ON plates(plate_number)
				WHERE deleted_at IS NULL;
			END IF;
		END $$;
	`).Error; err != nil {
		return fmt.Errorf("failed to add unique constraint for plate_number: %w", err)
	}

	if err := db.Exec(`
		DO $$ BEGIN
			IF NOT EXISTS (
				SELECT 1 FROM pg_indexes
				WHERE indexname = 'idx_plates_user_id'
			) THEN
				CREATE INDEX idx_plates_user_id
				ON plates(user_id)
				WHERE deleted_at IS NULL;
			END IF;
		END $$;
	`).Error; err != nil {
		return fmt.Errorf("failed to add index for user_id: %w", err)
	}

	return nil
}
