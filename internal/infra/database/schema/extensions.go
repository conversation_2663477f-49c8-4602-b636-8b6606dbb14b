package schema

import (
	"fmt"

	"gorm.io/gorm"
)

func EnableExtensions(db *gorm.DB) error {
	// Enable UUID extension for UUID generation
	if err := db.Exec(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`).Error; err != nil {
		return fmt.Errorf("failed to enable uuid-ossp extension: %w", err)
	}

	// Enable PostGIS extension for geospatial data
	if err := db.Exec(`CREATE EXTENSION IF NOT EXISTS "postgis";`).Error; err != nil {
		return fmt.Errorf("failed to enable postgis extension: %w", err)
	}

	return nil
}
