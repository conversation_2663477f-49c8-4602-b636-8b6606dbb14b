package infra

import (
	"crypto/tls"
	"fmt"

	"gopkg.in/gomail.v2"

	"github.com/smooth-inc/backend/configs"
	"github.com/smooth-inc/backend/internal/domain"
)

type EmailService struct {
	config  *configs.EmailConfig
	baseURL string
}

func NewEmailService(config *configs.EmailConfig, baseURL string) *EmailService {
	return &EmailService{
		config:  config,
		baseURL: baseURL,
	}
}

func (e *EmailService) SendEmail(to, subject, body string) error {
	// For development: log email instead of sending if host is invalid or empty credentials
	if e.config.Host == "" || e.config.Host == "smooth0.com" || e.config.Username == "" {
		fmt.Printf("📧 [DEV MODE] Email would be sent:\n")
		fmt.Printf("To: %s\n", to)
		fmt.Printf("From: %s <%s>\n", e.config.FromName, e.config.From)
		fmt.Printf("Subject: %s\n", subject)
		fmt.Printf("Body:\n%s\n", body)
		fmt.Printf("---\n")
		return nil
	}

	m := gomail.NewMessage()
	m.SetHeader("From", m.FormatAddress(e.config.From, e.config.FromName))
	m.SetHeader("To", to)
	m.SetHeader("Subject", subject)
	m.SetBody("text/plain", body)

	d := gomail.NewDialer(e.config.Host, e.config.Port, e.config.Username, e.config.Password)

	if e.config.TLS {
		d.TLSConfig = &tls.Config{
			ServerName:         e.config.Host,
			InsecureSkipVerify: false,
		}
	}

	if err := d.DialAndSend(m); err != nil {
		// Log the error but don't fail the API - fall back to development mode
		fmt.Printf("⚠️  [EMAIL ERROR] Failed to send email via SMTP, falling back to dev mode: %v\n", err)
		fmt.Printf("📧 [FALLBACK] Email would be sent:\n")
		fmt.Printf("To: %s\n", to)
		fmt.Printf("From: %s <%s>\n", e.config.FromName, e.config.From)
		fmt.Printf("Subject: %s\n", subject)
		fmt.Printf("Body:\n%s\n", body)
		fmt.Printf("---\n")
		return nil
	}

	fmt.Printf("✅ [EMAIL] Successfully sent email to %s\n", to)
	return nil
}

func (e *EmailService) SendEmailVerification(user *domain.User, token string) error {
	verificationURL := fmt.Sprintf("%s/auth/verify-email?token=%s", e.baseURL, token)

	subject := "Verify your email address - Smooth Parking"
	var body string

	if user.PreferredLanguage == domain.LanguageCodeJapanese {
		body = fmt.Sprintf(`こんにちは %s さん,

Smooth Parkingへのご登録ありがとうございます。

以下のリンクをクリックしてメールアドレスを確認してください：
%s

このリンクは24時間有効です。

よろしくお願いいたします。
Smooth Parking チーム`, user.Name, verificationURL)
	} else {
		body = fmt.Sprintf(`Hello %s,

Thank you for registering with Smooth Parking.

Please click the following link to verify your email address:
%s

This link will expire in 24 hours.

Best regards,
Smooth Parking Team`, user.Name, verificationURL)
	}

	return e.SendEmail(user.Email, subject, body)
}

func (e *EmailService) SendPasswordReset(user *domain.User, token string) error {
	resetURL := fmt.Sprintf("%s/auth/reset-password?token=%s", e.baseURL, token)

	subject := "Reset your password - Smooth Parking"
	var body string

	if user.PreferredLanguage == domain.LanguageCodeJapanese {
		body = fmt.Sprintf(`こんにちは %s さん,

パスワードリセットのリクエストを受け付けました。

以下のリンクをクリックして新しいパスワードを設定してください：
%s

このリンクは1時間有効です。

もしこのリクエストに心当たりがない場合は、このメールを無視してください。

よろしくお願いいたします。
Smooth Parking チーム`, user.Name, resetURL)
	} else {
		body = fmt.Sprintf(`Hello %s,

We received a request to reset your password.

Please click the following link to set a new password:
%s

This link will expire in 1 hour.

If you didn't request this password reset, please ignore this email.

Best regards,
Smooth Parking Team`, user.Name, resetURL)
	}

	return e.SendEmail(user.Email, subject, body)
}

func (e *EmailService) SendWelcomeEmail(user *domain.User) error {
	subject := "Welcome to Smooth Parking!"
	var body string

	if user.PreferredLanguage == domain.LanguageCodeJapanese {
		body = fmt.Sprintf(`こんにちは %s さん,

Smooth Parkingへようこそ！

メールアドレスの確認が完了しました。これで駐車場の予約やお支払いができるようになりました。

アプリをダウンロードして、快適な駐車体験をお楽しみください。

何かご質問がございましたら、お気軽にお問い合わせください。

よろしくお願いいたします。
Smooth Parking チーム`, user.Name)
	} else {
		body = fmt.Sprintf(`Hello %s,

Welcome to Smooth Parking!

Your email has been verified successfully. You can now start booking parking spaces and making payments.

Download our app to enjoy a seamless parking experience.

If you have any questions, please don't hesitate to contact us.

Best regards,
Smooth Parking Team`, user.Name)
	}

	return e.SendEmail(user.Email, subject, body)
}

func (e *EmailService) SendTemplatedEmail(template string, to string, data map[string]interface{}) error {
	// For now, implement basic template support
	// In the future, this could use a proper template engine
	subject := "Notification from Smooth Parking"
	body := template

	// Simple template variable replacement
	for key, value := range data {
		placeholder := fmt.Sprintf("{{%s}}", key)
		body = fmt.Sprintf(body, placeholder, fmt.Sprintf("%v", value))
	}

	return e.SendEmail(to, subject, body)
}

func (e *EmailService) SendEmailChangeVerification(user *domain.User, newEmail, token string) error {
	verificationURL := fmt.Sprintf("%s/auth/verify-email-change?token=%s", e.baseURL, token)

	subject := "Verify your new email address - Smooth Parking"
	var body string

	if user.PreferredLanguage == domain.LanguageCodeJapanese {
		body = fmt.Sprintf(`こんにちは %s さん,

メールアドレスの変更リクエストを受け付けました。

新しいメールアドレス: %s

以下のリンクをクリックして新しいメールアドレスを確認してください：
%s

このリンクは24時間有効です。

もしこのリクエストに心当たりがない場合は、このメールを無視してください。

よろしくお願いいたします。
Smooth Parking チーム`, user.Name, newEmail, verificationURL)
	} else {
		body = fmt.Sprintf(`Hello %s,

We received a request to change your email address.

New email address: %s

Please click the following link to verify your new email address:
%s

This link will expire in 24 hours.

If you didn't request this email change, please ignore this email.

Best regards,
Smooth Parking Team`, user.Name, newEmail, verificationURL)
	}

	return e.SendEmail(newEmail, subject, body)
}

func (e *EmailService) SendPaymentMethodAddedEmail(user *domain.User) error {
	subject := "Payment Method Added Successfully - Smooth Parking"
	var body string

	if user.PreferredLanguage == domain.LanguageCodeJapanese {
		body = fmt.Sprintf(`こんにちは %s さん,

お支払い方法の追加が完了いたしました。

これで自動決済が利用可能になりました。

よろしくお願いいたします。
Smooth Parking チーム`, user.Name)
	} else {
		body = fmt.Sprintf(`Hello %s,

Your payment method has been successfully added to your account.

You can now use automatic payments for parking sessions.

Best regards,
Smooth Parking Team`, user.Name)
	}

	return e.SendEmail(user.Email, subject, body)
}

func (e *EmailService) SendSetupIntentCanceledEmail(user *domain.User) error {
	subject := "Payment Method Setup Canceled - Smooth Parking"
	var body string

	if user.PreferredLanguage == domain.LanguageCodeJapanese {
		body = fmt.Sprintf(`こんにちは %s さん,

お支払い方法の設定がキャンセルされました。

自動決済をご利用になりたい場合は、再度お支払い方法を追加してください。

よろしくお願いいたします。
Smooth Parking チーム`, user.Name)
	} else {
		body = fmt.Sprintf(`Hello %s,

Your payment method setup was canceled.

If you'd like to use automatic payments, please try adding a payment method again.

Best regards,
Smooth Parking Team`, user.Name)
	}

	return e.SendEmail(user.Email, subject, body)
}

func (e *EmailService) SendSetupIntentRequiresActionEmail(user *domain.User) error {
	subject := "Payment Method Setup Requires Action - Smooth Parking"
	var body string

	if user.PreferredLanguage == domain.LanguageCodeJapanese {
		body = fmt.Sprintf(`こんにちは %s さん,

お支払い方法の設定で追加の認証が必要です。

アプリまたはウェブサイトで認証を完了してください。

よろしくお願いいたします。
Smooth Parking チーム`, user.Name)
	} else {
		body = fmt.Sprintf(`Hello %s,

Your payment method setup requires additional authentication.

Please complete the authentication in the app or website.

Best regards,
Smooth Parking Team`, user.Name)
	}

	return e.SendEmail(user.Email, subject, body)
}

func (e *EmailService) SendSetupIntentFailedEmail(user *domain.User, errorMessage string) error {
	subject := "Payment Method Setup Failed - Smooth Parking"
	var body string

	if user.PreferredLanguage == domain.LanguageCodeJapanese {
		body = fmt.Sprintf(`こんにちは %s さん,

お支払い方法の設定に失敗いたしました。

エラー: %s

別のお支払い方法をお試しいただくか、カスタマーサポートまでお問い合わせください。

よろしくお願いいたします。
Smooth Parking チーム`, user.Name, errorMessage)
	} else {
		body = fmt.Sprintf(`Hello %s,

Your payment method setup failed.

Error: %s

Please try a different payment method or contact customer support.

Best regards,
Smooth Parking Team`, user.Name, errorMessage)
	}

	return e.SendEmail(user.Email, subject, body)
}

func (e *EmailService) SendPaymentFailedEmail(user *domain.User, payment *domain.Payment) error {
	subject := "Payment Failed - Smooth Parking"
	var body string

	failureReason := "Unknown error"
	if payment.FailureReason != nil {
		failureReason = *payment.FailureReason
	}

	if user.PreferredLanguage == domain.LanguageCodeJapanese {
		body = fmt.Sprintf(`こんにちは %s さん,

駐車料金のお支払いに失敗いたしました。

金額: %s
失敗理由: %s

お支払い方法をご確認の上、再度お試しください。

よろしくお願いいたします。
Smooth Parking チーム`, user.Name, payment.GetAmountInCurrency(), failureReason)
	} else {
		body = fmt.Sprintf(`Hello %s,

Your parking payment has failed.

Amount: %s
Failure Reason: %s

Please check your payment method and try again.

Best regards,
Smooth Parking Team`, user.Name, payment.GetAmountInCurrency(), failureReason)
	}

	return e.SendEmail(user.Email, subject, body)
}
