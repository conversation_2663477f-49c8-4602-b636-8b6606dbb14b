package infra

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
)

type JWTService struct {
	secretKey              []byte
	accessTokenExpiration  time.Duration
	refreshTokenExpiration time.Duration
	issuer                 string
}

func NewJWTService(secretKey string, accessTokenExpiration, refreshTokenExpiration time.Duration, issuer string) *JWTService {
	return &JWTService{
		secretKey:              []byte(secretKey),
		accessTokenExpiration:  accessTokenExpiration,
		refreshTokenExpiration: refreshTokenExpiration,
		issuer:                 issuer,
	}
}

func (j *JWTService) GenerateTokenPair(user *domain.User, sessionID uuid.UUID) (*domain.TokenPair, error) {
	now := time.Now()
	accessTokenExpiry := now.Add(j.accessTokenExpiration)
	refreshTokenExpiry := now.Add(j.refreshTokenExpiration)

	accessClaims := &domain.JWTClaims{
		UserID:            user.ID,
		Email:             user.Email,
		Username:          user.Username,
		Role:              user.Role,
		PreferredLanguage: user.PreferredLanguage,
		TokenType:         domain.TokenTypeAccess,
		SessionID:         sessionID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(accessTokenExpiry),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    j.issuer,
			Subject:   user.ID.String(),
			ID:        uuid.New().String(),
		},
	}

	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
	accessTokenString, err := accessToken.SignedString(j.secretKey)
	if err != nil {
		return nil, err
	}

	refreshClaims := &domain.JWTClaims{
		UserID:            user.ID,
		Email:             user.Email,
		Username:          user.Username,
		Role:              user.Role,
		PreferredLanguage: user.PreferredLanguage,
		TokenType:         domain.TokenTypeRefresh,
		SessionID:         sessionID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(refreshTokenExpiry),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    j.issuer,
			Subject:   user.ID.String(),
			ID:        uuid.New().String(),
		},
	}

	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	refreshTokenString, err := refreshToken.SignedString(j.secretKey)
	if err != nil {
		return nil, err
	}

	return &domain.TokenPair{
		AccessToken:           accessTokenString,
		RefreshToken:          refreshTokenString,
		AccessTokenExpiresAt:  accessTokenExpiry,
		RefreshTokenExpiresAt: refreshTokenExpiry,
	}, nil
}

func (j *JWTService) ValidateToken(tokenString string) (*domain.JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &domain.JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("unexpected signing method")
		}
		return j.secretKey, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*domain.JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

func (j *JWTService) ValidateAccessToken(tokenString string) (*domain.JWTClaims, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return nil, err
	}

	if claims.TokenType != domain.TokenTypeAccess {
		return nil, errors.New("invalid token type")
	}

	return claims, nil
}

func (j *JWTService) ValidateRefreshToken(tokenString string) (*domain.JWTClaims, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return nil, err
	}

	if claims.TokenType != domain.TokenTypeRefresh {
		return nil, errors.New("invalid token type")
	}

	return claims, nil
}
