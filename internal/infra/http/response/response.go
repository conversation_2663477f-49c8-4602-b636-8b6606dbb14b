package response

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

type Response struct {
	Success   bool        `json:"success"`
	Data      interface{} `json:"data,omitempty"`
	Error     *ErrorInfo  `json:"error,omitempty"`
	Meta      *Meta       `json:"meta,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
	RequestID string      `json:"request_id,omitempty"`
}

type ErrorInfo struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Details interface{} `json:"details,omitempty"`
}

type Meta struct {
	Page       int `json:"page,omitempty"`
	Limit      int `json:"limit,omitempty"`
	Total      int `json:"total,omitempty"`
	TotalPages int `json:"total_pages,omitempty"`
}

func Success(c *gin.Context, data interface{}) {
	response := Response{
		Success:   true,
		Data:      data,
		Timestamp: time.Now().UTC(),
		RequestID: getRequestID(c),
	}
	c.<PERSON>(http.StatusOK, response)
}

func SuccessWithMeta(c *gin.Context, data interface{}, meta *Meta) {
	response := Response{
		Success:   true,
		Data:      data,
		Meta:      meta,
		Timestamp: time.Now().UTC(),
		RequestID: getRequestID(c),
	}
	c.JSON(http.StatusOK, response)
}

func Created(c *gin.Context, data interface{}) {
	response := Response{
		Success:   true,
		Data:      data,
		Timestamp: time.Now().UTC(),
		RequestID: getRequestID(c),
	}
	c.JSON(http.StatusCreated, response)
}

func NoContent(c *gin.Context) {
	c.Status(http.StatusNoContent)
}

func BadRequest(c *gin.Context, code, message string, details interface{}) {
	sendError(c, http.StatusBadRequest, code, message, details)
}

func Unauthorized(c *gin.Context, code, message string) {
	sendError(c, http.StatusUnauthorized, code, message, nil)
}

func Forbidden(c *gin.Context, code, message string) {
	sendError(c, http.StatusForbidden, code, message, nil)
}

func NotFound(c *gin.Context, code, message string) {
	sendError(c, http.StatusNotFound, code, message, nil)
}

func Conflict(c *gin.Context, code, message string, details interface{}) {
	sendError(c, http.StatusConflict, code, message, details)
}

func UnprocessableEntity(c *gin.Context, code, message string, details interface{}) {
	sendError(c, http.StatusUnprocessableEntity, code, message, details)
}

func InternalServerError(c *gin.Context, code, message string) {
	sendError(c, http.StatusInternalServerError, code, message, nil)
}

func ServiceUnavailable(c *gin.Context, code, message string) {
	sendError(c, http.StatusServiceUnavailable, code, message, nil)
}

func sendError(c *gin.Context, statusCode int, code, message string, details interface{}) {
	response := Response{
		Success: false,
		Error: &ErrorInfo{
			Code:    code,
			Message: message,
			Details: details,
		},
		Timestamp: time.Now().UTC(),
		RequestID: getRequestID(c),
	}
	c.JSON(statusCode, response)
}

func getRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}

type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Value   string `json:"value,omitempty"`
}

func ValidationErrors(c *gin.Context, errors []ValidationError) {
	BadRequest(c, "VALIDATION_ERROR", "Validation failed", errors)
}
