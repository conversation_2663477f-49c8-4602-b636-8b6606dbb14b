package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/smooth-inc/backend/internal/infra/logger"
)

type ValidationConfig struct {
	MaxRequestSize    int64    `json:"max_request_size"`
	AllowedMethods    []string `json:"allowed_methods"`
	RequiredHeaders   []string `json:"required_headers"`
	AllowedOrigins    []string `json:"allowed_origins"`
	BlockedPatterns   []string `json:"blocked_patterns"`
	EnableSQLInjCheck bool     `json:"enable_sql_inj_check"`
	EnableXSSCheck    bool     `json:"enable_xss_check"`
	MaxDepth          int      `json:"max_depth"`
	MaxKeys           int      `json:"max_keys"`
}

type SecurityPattern struct {
	Pattern     *regexp.Regexp
	Description string
	Severity    string
}

var (
	sqlInjectionPatterns = []SecurityPattern{
		{regexp.MustCompile(`(?i)(union\s+select|select\s+.*\s+from|insert\s+into|delete\s+from|update\s+.*\s+set|drop\s+table)`), "SQL Injection", "HIGH"},
		{regexp.MustCompile(`(?i)(\'\s*(or|and)\s*\'\s*=\s*\'|1\s*=\s*1|\'\s*or\s*\'1\'\s*=\s*\'1|\'\s*and\s*\'1\'\s*=\s*\'1)`), "SQL Injection Boolean", "HIGH"},
		{regexp.MustCompile(`(?i)(--\s|\/\*.*\*\/|;\s*(drop|delete|update|insert|select))`), "SQL Comment/Termination", "MEDIUM"},
	}

	xssPatterns = []SecurityPattern{
		{regexp.MustCompile(`(?i)(<script[^>]*>.*?</script>|javascript:|vbscript:|onload=|onerror=)`), "XSS Script", "HIGH"},
		{regexp.MustCompile(`(?i)(<iframe|<object|<embed|<link|<meta)`), "XSS HTML Tags", "MEDIUM"},
		{regexp.MustCompile(`(?i)(expression\s*\(|@import|behaviour:)`), "XSS CSS", "MEDIUM"},
	}
)

func RequestValidationMiddleware(config ValidationConfig, log *logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := validateRequest(c, config); err != nil {
			log.LogWarn(c.Request.Context(), "Request validation failed", map[string]interface{}{
				"error":  err.Error(),
				"method": c.Request.Method,
				"path":   c.Request.URL.Path,
				"ip":     c.ClientIP(),
			})

			c.JSON(http.StatusBadRequest, gin.H{
				"error": gin.H{
					"code":    "VALIDATION_ERROR",
					"message": err.Error(),
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

func validateRequest(c *gin.Context, config ValidationConfig) error {
	if err := validateMethod(c, config.AllowedMethods); err != nil {
		return err
	}

	if err := validateHeaders(c, config.RequiredHeaders); err != nil {
		return err
	}

	if err := validateRequestSize(c, config.MaxRequestSize); err != nil {
		return err
	}

	if err := validateContentType(c); err != nil {
		return err
	}

	if c.Request.Body != nil && (c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH") {
		if err := validateRequestBody(c, config); err != nil {
			return err
		}
	}

	return nil
}

func validateMethod(c *gin.Context, allowedMethods []string) error {
	if len(allowedMethods) == 0 {
		return nil
	}

	method := c.Request.Method
	for _, allowed := range allowedMethods {
		if method == allowed {
			return nil
		}
	}

	return fmt.Errorf("method %s not allowed", method)
}

func validateHeaders(c *gin.Context, requiredHeaders []string) error {
	for _, header := range requiredHeaders {
		if c.GetHeader(header) == "" {
			return fmt.Errorf("required header %s is missing", header)
		}
	}
	return nil
}

func validateRequestSize(c *gin.Context, maxSize int64) error {
	if maxSize <= 0 {
		return nil
	}

	if c.Request.ContentLength > maxSize {
		return fmt.Errorf("request body too large: %d bytes (max: %d)", c.Request.ContentLength, maxSize)
	}

	return nil
}

func validateContentType(c *gin.Context) error {
	if c.Request.Method == "GET" || c.Request.Method == "DELETE" {
		return nil
	}

	contentType := c.GetHeader("Content-Type")
	if contentType == "" {
		return fmt.Errorf("content-type header is required")
	}

	validTypes := []string{
		"application/json",
		"application/x-www-form-urlencoded",
		"multipart/form-data",
	}

	for _, validType := range validTypes {
		if strings.Contains(contentType, validType) {
			return nil
		}
	}

	return fmt.Errorf("unsupported content-type: %s", contentType)
}

func validateRequestBody(c *gin.Context, config ValidationConfig) error {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return fmt.Errorf("failed to read request body: %w", err)
	}

	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

	if len(body) == 0 {
		return nil
	}

	bodyStr := string(body)
	isJSON := strings.Contains(c.GetHeader("Content-Type"), "application/json")

	if config.EnableSQLInjCheck {
		if err := checkSecurityPatternsWithContext(bodyStr, sqlInjectionPatterns, isJSON); err != nil {
			return err
		}
	}

	if config.EnableXSSCheck {
		if err := checkSecurityPatternsWithContext(bodyStr, xssPatterns, isJSON); err != nil {
			return err
		}
	}

	if isJSON {
		if err := validateJSONStructure(body, config); err != nil {
			return err
		}
	}

	return nil
}

func checkSecurityPatterns(input string, patterns []SecurityPattern) error {
	return checkSecurityPatternsWithContext(input, patterns, false)
}

func checkSecurityPatternsWithContext(input string, patterns []SecurityPattern, isJSON bool) error {
	for _, pattern := range patterns {
		if pattern.Pattern.MatchString(input) {
			if isJSON && isLikelyJSONFalsePositive(input, pattern) {
				continue
			}
			return fmt.Errorf("potential security threat detected: %s", pattern.Description)
		}
	}
	return nil
}

func isLikelyJSONFalsePositive(input string, pattern SecurityPattern) bool {
	var jsonData interface{}
	if err := json.Unmarshal([]byte(input), &jsonData); err != nil {
		return false
	}

	if pattern.Description == "SQL Injection Boolean" {
		return !containsSQLInjectionInJSON(input)
	}

	if pattern.Description == "SQL Comment/Termination" {
		return !containsSuspiciousComments(input)
	}

	return false
}

func containsSQLInjectionInJSON(input string) bool {
	sqlInjectionInJSONPattern := regexp.MustCompile(`(?i)"\s*[^"]*\s*('\s*(or|and)\s*'[^"]*=|1\s*=\s*1|'\s*or\s*'1'\s*=\s*'1)[^"]*"`)
	return sqlInjectionInJSONPattern.MatchString(input)
}

func containsSuspiciousComments(input string) bool {
	suspiciousCommentPattern := regexp.MustCompile(`(?i)(--\s+(drop|delete|update|insert|select|union)|\/\*\s*(drop|delete|update|insert|select|union))`)
	return suspiciousCommentPattern.MatchString(input)
}

func validateJSONStructure(body []byte, config ValidationConfig) error {
	var data interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return fmt.Errorf("invalid JSON format: %w", err)
	}

	if config.MaxDepth > 0 || config.MaxKeys > 0 {
		if err := validateJSONComplexity(data, 0, config.MaxDepth, config.MaxKeys); err != nil {
			return err
		}
	}

	return nil
}

func validateJSONComplexity(data interface{}, currentDepth, maxDepth, maxKeys int) error {
	if maxDepth > 0 && currentDepth > maxDepth {
		return fmt.Errorf("JSON depth exceeds maximum allowed depth of %d", maxDepth)
	}

	switch v := data.(type) {
	case map[string]interface{}:
		if maxKeys > 0 && len(v) > maxKeys {
			return fmt.Errorf("JSON object has too many keys: %d (max: %d)", len(v), maxKeys)
		}
		for _, value := range v {
			if err := validateJSONComplexity(value, currentDepth+1, maxDepth, maxKeys); err != nil {
				return err
			}
		}
	case []interface{}:
		if maxKeys > 0 && len(v) > maxKeys {
			return fmt.Errorf("JSON array has too many elements: %d (max: %d)", len(v), maxKeys)
		}
		for _, value := range v {
			if err := validateJSONComplexity(value, currentDepth+1, maxDepth, maxKeys); err != nil {
				return err
			}
		}
	}

	return nil
}

func DefaultValidationConfig() ValidationConfig {
	return ValidationConfig{
		MaxRequestSize:    1024 * 1024 * 10, // 10MB
		AllowedMethods:    []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		RequiredHeaders:   []string{},
		EnableSQLInjCheck: true,
		EnableXSSCheck:    true,
		MaxDepth:          10,
		MaxKeys:           100,
	}
}

func StrictValidationConfig() ValidationConfig {
	return ValidationConfig{
		MaxRequestSize:    1024 * 1024 * 2, // 2MB
		AllowedMethods:    []string{"GET", "POST", "PUT", "PATCH", "DELETE"},
		RequiredHeaders:   []string{"Content-Type"},
		EnableSQLInjCheck: true,
		EnableXSSCheck:    true,
		MaxDepth:          5,
		MaxKeys:           50,
	}
}
