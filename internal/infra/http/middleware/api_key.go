package middleware

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/smooth-inc/backend/internal/infra/logger"
)

type APIKeyConfig struct {
	HeaderName       string            `json:"header_name"`
	SignatureHeader  string            `json:"signature_header"`
	TimestampHeader  string            `json:"timestamp_header"`
	MaxTimeDrift     time.Duration     `json:"max_time_drift"`
	RequireSignature bool              `json:"require_signature"`
	Keys             map[string]APIKey `json:"keys"`
}

type APIKey struct {
	Key         string            `json:"key"`
	Secret      string            `json:"secret"`
	Name        string            `json:"name"`
	Permissions []string          `json:"permissions"`
	RateLimit   int               `json:"rate_limit"`
	Enabled     bool              `json:"enabled"`
	ExpiresAt   *time.Time        `json:"expires_at"`
	Metadata    map[string]string `json:"metadata"`
}

func APIKeyAuthMiddleware(config APIKeyConfig, log *logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		apiKey := c.GetHeader(config.HeaderName)
		if apiKey == "" {
			log.LogWarn(c.Request.Context(), "Missing API key", map[string]interface{}{
				"path":   c.Request.URL.Path,
				"method": c.Request.Method,
				"ip":     c.ClientIP(),
			})

			c.JSON(http.StatusUnauthorized, gin.H{
				"error": gin.H{
					"code":    "MISSING_API_KEY",
					"message": "API key is required",
				},
			})
			c.Abort()
			return
		}

		keyData, exists := config.Keys[apiKey]
		if !exists || !keyData.Enabled {
			log.LogWarn(c.Request.Context(), "Invalid API key", map[string]interface{}{
				"api_key": maskAPIKey(apiKey),
				"path":    c.Request.URL.Path,
				"method":  c.Request.Method,
				"ip":      c.ClientIP(),
			})

			c.JSON(http.StatusUnauthorized, gin.H{
				"error": gin.H{
					"code":    "INVALID_API_KEY",
					"message": "Invalid or disabled API key",
				},
			})
			c.Abort()
			return
		}

		if keyData.ExpiresAt != nil && time.Now().After(*keyData.ExpiresAt) {
			log.LogWarn(c.Request.Context(), "Expired API key", map[string]interface{}{
				"api_key":    maskAPIKey(apiKey),
				"expires_at": keyData.ExpiresAt,
				"path":       c.Request.URL.Path,
				"method":     c.Request.Method,
			})

			c.JSON(http.StatusUnauthorized, gin.H{
				"error": gin.H{
					"code":    "EXPIRED_API_KEY",
					"message": "API key has expired",
				},
			})
			c.Abort()
			return
		}

		if config.RequireSignature {
			if err := validateSignature(c, keyData, config); err != nil {
				log.LogWarn(c.Request.Context(), "Invalid signature", map[string]interface{}{
					"api_key": maskAPIKey(apiKey),
					"error":   err.Error(),
					"path":    c.Request.URL.Path,
					"method":  c.Request.Method,
				})

				c.JSON(http.StatusUnauthorized, gin.H{
					"error": gin.H{
						"code":    "INVALID_SIGNATURE",
						"message": "Request signature validation failed",
					},
				})
				c.Abort()
				return
			}
		}

		c.Set("api_key", apiKey)
		c.Set("api_key_data", keyData)
		c.Set("api_key_name", keyData.Name)
		c.Set("api_key_permissions", keyData.Permissions)

		log.LogInfo(c.Request.Context(), "API key authenticated", map[string]interface{}{
			"api_key_name": keyData.Name,
			"permissions":  keyData.Permissions,
			"path":         c.Request.URL.Path,
			"method":       c.Request.Method,
		})

		c.Next()
	}
}

func validateSignature(c *gin.Context, keyData APIKey, config APIKeyConfig) error {
	signature := c.GetHeader(config.SignatureHeader)
	timestamp := c.GetHeader(config.TimestampHeader)

	if signature == "" {
		return fmt.Errorf("missing signature header")
	}

	if timestamp == "" {
		return fmt.Errorf("missing timestamp header")
	}

	ts, err := strconv.ParseInt(timestamp, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid timestamp format")
	}

	requestTime := time.Unix(ts, 0)
	now := time.Now()

	if now.Sub(requestTime) > config.MaxTimeDrift || requestTime.Sub(now) > config.MaxTimeDrift {
		return fmt.Errorf("timestamp outside allowed drift")
	}

	payload := fmt.Sprintf("%s\n%s\n%s\n%s",
		c.Request.Method,
		c.Request.URL.Path,
		timestamp,
		c.GetHeader("Content-Type"))

	expectedSig := generateSignature(payload, keyData.Secret)
	if !hmac.Equal([]byte(signature), []byte(expectedSig)) {
		return fmt.Errorf("signature mismatch")
	}

	return nil
}

func generateSignature(payload, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(payload))
	return hex.EncodeToString(h.Sum(nil))
}

func maskAPIKey(key string) string {
	if len(key) <= 8 {
		return strings.Repeat("*", len(key))
	}
	return key[:4] + strings.Repeat("*", len(key)-8) + key[len(key)-4:]
}

func RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		permissions, exists := c.Get("api_key_permissions")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{
				"error": gin.H{
					"code":    "PERMISSIONS_NOT_FOUND",
					"message": "API key permissions not found",
				},
			})
			c.Abort()
			return
		}

		perms, ok := permissions.([]string)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": gin.H{
					"code":    "INVALID_PERMISSIONS_FORMAT",
					"message": "Invalid permissions format",
				},
			})
			c.Abort()
			return
		}

		hasPermission := false
		for _, perm := range perms {
			if perm == permission || perm == "*" {
				hasPermission = true
				break
			}
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"error": gin.H{
					"code":    "INSUFFICIENT_PERMISSIONS",
					"message": fmt.Sprintf("Permission '%s' required", permission),
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

func CreateHardwareAPIKey(name, keyValue, secret string) APIKey {
	return APIKey{
		Key:         keyValue,
		Secret:      secret,
		Name:        name,
		Permissions: []string{"hardware:detection", "hardware:upload"},
		RateLimit:   1000,
		Enabled:     true,
		Metadata: map[string]string{
			"type":        "hardware",
			"description": "Hardware device API key",
		},
	}
}

func CreateAdminAPIKey(name, keyValue, secret string) APIKey {
	return APIKey{
		Key:         keyValue,
		Secret:      secret,
		Name:        name,
		Permissions: []string{"*"},
		RateLimit:   10000,
		Enabled:     true,
		Metadata: map[string]string{
			"type":        "admin",
			"description": "Administrative API key",
		},
	}
}

func CreateReadOnlyAPIKey(name, keyValue, secret string) APIKey {
	return APIKey{
		Key:         keyValue,
		Secret:      secret,
		Name:        name,
		Permissions: []string{"read:*"},
		RateLimit:   500,
		Enabled:     true,
		Metadata: map[string]string{
			"type":        "readonly",
			"description": "Read-only API key",
		},
	}
}

func DefaultAPIKeyConfig() APIKeyConfig {
	return APIKeyConfig{
		HeaderName:       "X-API-Key",
		SignatureHeader:  "X-Signature",
		TimestampHeader:  "X-Timestamp",
		MaxTimeDrift:     time.Minute * 5,
		RequireSignature: false,
		Keys:             make(map[string]APIKey),
	}
}

func SecureAPIKeyConfig() APIKeyConfig {
	return APIKeyConfig{
		HeaderName:       "X-API-Key",
		SignatureHeader:  "X-Signature",
		TimestampHeader:  "X-Timestamp",
		MaxTimeDrift:     time.Minute * 2,
		RequireSignature: true,
		Keys:             make(map[string]APIKey),
	}
}
