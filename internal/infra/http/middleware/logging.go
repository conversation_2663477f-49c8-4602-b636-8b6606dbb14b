package middleware

import (
	"bytes"
	"io"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/smooth-inc/backend/internal/infra/logger"
)

func LoggingMiddleware(log *logger.Logger) gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// We handle logging in the custom middleware below
		return ""
	})
}

func RequestLoggingMiddleware(log *logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		requestID := logger.GenerateRequestID()
		c.Set("request_id", requestID)

		ctx := logger.WithRequestID(c.Request.Context(), requestID)
		c.Request = c.Request.WithContext(ctx)

		var requestBody []byte
		if c.Request.Body != nil && shouldLogRequestBody(c) {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.New<PERSON>uffer(requestBody))
		}

		responseWriter := &responseWriter{
			ResponseWriter: c.Writer,
			body:           &bytes.Buffer{},
		}
		c.Writer = responseWriter

		log.WithHTTPRequest(ctx, c).WithFields(map[string]interface{}{
			"request_body_size": len(requestBody),
		}).Info("Request started")

		c.Next()

		duration := time.Since(start)
		statusCode := c.Writer.Status()
		responseSize := responseWriter.body.Len()

		log.LogHTTPRequest(ctx, c, duration, statusCode, int64(responseSize))

		// Log errors if any
		if len(c.Errors) > 0 {
			for _, err := range c.Errors {
				log.LogError(ctx, err.Err, "Request error occurred", map[string]interface{}{
					"error_type": err.Type,
					"error_meta": err.Meta,
				})
			}
		}
	}
}

type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

func (w *responseWriter) WriteString(s string) (int, error) {
	w.body.WriteString(s)
	return w.ResponseWriter.WriteString(s)
}

func shouldLogRequestBody(c *gin.Context) bool {
	contentLength := c.Request.ContentLength

	if contentLength > 1024*1024 {
		return false
	}

	contentType := c.GetHeader("Content-Type")
	if contentType == "multipart/form-data" || contentType == "application/octet-stream" {
		return false
	}

	path := c.Request.URL.Path
	sensitiveEndpoints := []string{
		"/auth/login",
		"/auth/register",
		"/users/password",
	}

	for _, endpoint := range sensitiveEndpoints {
		if path == endpoint {
			return false
		}
	}

	return true
}
