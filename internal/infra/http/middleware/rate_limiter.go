package middleware

import (
	"fmt"
	"net/http"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

type RateLimiterConfig struct {
	RequestsPerSecond int           `json:"requests_per_second"`
	BurstSize         int           `json:"burst_size"`
	WindowSize        time.Duration `json:"window_size"`
	CleanupInterval   time.Duration `json:"cleanup_interval"`
}

type TokenBucket struct {
	tokens     int
	maxTokens  int
	refillRate int
	lastRefill time.Time
	mutex      sync.Mutex
}

func NewTokenBucket(maxTokens, refillRate int) *TokenBucket {
	return &TokenBucket{
		tokens:     maxTokens,
		maxTokens:  maxTokens,
		refillRate: refillRate,
		lastRefill: time.Now(),
	}
}

func (tb *TokenBucket) Allow() bool {
	tb.mutex.Lock()
	defer tb.mutex.Unlock()

	now := time.Now()
	elapsed := now.Sub(tb.lastRefill)

	tokensToAdd := int(elapsed.Seconds()) * tb.refillRate
	tb.tokens += tokensToAdd
	if tb.tokens > tb.maxTokens {
		tb.tokens = tb.maxTokens
	}
	tb.lastRefill = now

	if tb.tokens > 0 {
		tb.tokens--
		return true
	}
	return false
}

func (tb *TokenBucket) Tokens() int {
	tb.mutex.Lock()
	defer tb.mutex.Unlock()
	return tb.tokens
}

type RateLimiter struct {
	config   RateLimiterConfig
	buckets  map[string]*TokenBucket
	mutex    sync.RWMutex
	lastSeen map[string]time.Time
}

func NewRateLimiter(config RateLimiterConfig) *RateLimiter {
	rl := &RateLimiter{
		config:   config,
		buckets:  make(map[string]*TokenBucket),
		lastSeen: make(map[string]time.Time),
	}

	if config.CleanupInterval > 0 {
		go rl.cleanupRoutine()
	}

	return rl
}

func (rl *RateLimiter) getBucket(key string) *TokenBucket {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	bucket, exists := rl.buckets[key]
	if !exists {
		bucket = NewTokenBucket(rl.config.BurstSize, rl.config.RequestsPerSecond)
		rl.buckets[key] = bucket
	}

	rl.lastSeen[key] = time.Now()
	return bucket
}

func (rl *RateLimiter) cleanupRoutine() {
	ticker := time.NewTicker(rl.config.CleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		rl.cleanup()
	}
}

func (rl *RateLimiter) cleanup() {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	cutoff := time.Now().Add(-rl.config.WindowSize)
	for key, lastSeen := range rl.lastSeen {
		if lastSeen.Before(cutoff) {
			delete(rl.buckets, key)
			delete(rl.lastSeen, key)
		}
	}
}

func RateLimitMiddleware(config RateLimiterConfig) gin.HandlerFunc {
	limiter := NewRateLimiter(config)

	return func(c *gin.Context) {
		key := getClientKey(c)
		bucket := limiter.getBucket(key)

		if !bucket.Allow() {
			c.Header("X-Rate-Limit-Limit", strconv.Itoa(config.RequestsPerSecond))
			c.Header("X-Rate-Limit-Remaining", "0")
			c.Header("X-Rate-Limit-Reset", strconv.FormatInt(time.Now().Add(time.Second).Unix(), 10))

			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": gin.H{
					"code":    "RATE_LIMIT_EXCEEDED",
					"message": "Rate limit exceeded. Please try again later.",
				},
			})
			c.Abort()
			return
		}

		remaining := bucket.Tokens()
		if remaining < 0 {
			remaining = 0
		}

		c.Header("X-Rate-Limit-Limit", strconv.Itoa(config.RequestsPerSecond))
		c.Header("X-Rate-Limit-Remaining", strconv.Itoa(remaining))
		c.Header("X-Rate-Limit-Reset", strconv.FormatInt(time.Now().Add(time.Second).Unix(), 10))

		c.Next()
	}
}

func getClientKey(c *gin.Context) string {
	if userID, exists := c.Get("user_id"); exists {
		return fmt.Sprintf("user:%v", userID)
	}

	if apiKey := c.GetHeader("X-API-Key"); apiKey != "" {
		return fmt.Sprintf("api_key:%s", apiKey)
	}

	return fmt.Sprintf("ip:%s", c.ClientIP())
}

func DefaultRateLimiterConfig() RateLimiterConfig {
	return RateLimiterConfig{
		RequestsPerSecond: 100,
		BurstSize:         20,
		WindowSize:        time.Hour,
		CleanupInterval:   time.Minute * 10,
	}
}

func StrictRateLimiterConfig() RateLimiterConfig {
	return RateLimiterConfig{
		RequestsPerSecond: 10,
		BurstSize:         5,
		WindowSize:        time.Hour,
		CleanupInterval:   time.Minute * 10,
	}
}
