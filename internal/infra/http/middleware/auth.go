package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

func JWTAuthMiddleware(jwtService usecase.JWTService, log *logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			log.LogWarn(c.Request.Context(), "Missing Authorization header", map[string]interface{}{
				"path":   c.Request.URL.Path,
				"method": c.Request.Method,
			})
			c.J<PERSON>(http.StatusUnauthorized, gin.H{
				"error": gin.H{
					"code":    "UNAUTHORIZED",
					"message": "Authorization header is required",
				},
			})
			c.Abort()
			return
		}

		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			log.LogWarn(c.Request.Context(), "Invalid Authorization header format", map[string]interface{}{
				"path":   c.Request.URL.Path,
				"method": c.Request.Method,
				"header": authHeader,
			})
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": gin.H{
					"code":    "UNAUTHORIZED",
					"message": "Invalid authorization header format",
				},
			})
			c.Abort()
			return
		}

		token := tokenParts[1]
		var claims *domain.JWTClaims
		claims, err := jwtService.ValidateAccessToken(token)
		if err != nil {
			log.LogWarn(c.Request.Context(), "Invalid JWT token", map[string]interface{}{
				"path":  c.Request.URL.Path,
				"method": c.Request.Method,
				"error": err.Error(),
			})
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": gin.H{
					"code":    "UNAUTHORIZED",
					"message": "Invalid or expired token",
				},
			})
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user_id", claims.UserID)
		c.Set("session_id", claims.SessionID)
		c.Set("user_email", claims.Email)
		c.Set("user_username", claims.Username)
		c.Set("user_role", claims.Role)

		// Update context with user ID for logging
		ctx := logger.WithUserID(c.Request.Context(), claims.UserID.String())
		c.Request = c.Request.WithContext(ctx)

		log.LogInfo(c.Request.Context(), "User authenticated successfully", map[string]interface{}{
			"user_id":    claims.UserID,
			"session_id": claims.SessionID,
			"username":   claims.Username,
			"path":       c.Request.URL.Path,
			"method":     c.Request.Method,
		})

		c.Next()
	}
}
