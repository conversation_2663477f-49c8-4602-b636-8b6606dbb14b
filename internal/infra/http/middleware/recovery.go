package middleware

import (
	"fmt"
	"runtime/debug"

	"github.com/gin-gonic/gin"
	"github.com/smooth-inc/backend/internal/infra/http/response"
	"github.com/smooth-inc/backend/internal/infra/logger"
)

func RecoveryMiddleware(log *logger.Logger) gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		ctx := c.Request.Context()

		log.LogError(ctx, fmt.Errorf("panic recovered: %v", recovered), "Panic occurred", map[string]interface{}{
			"stack_trace": string(debug.Stack()),
			"method":      c.Request.Method,
			"path":        c.Request.URL.Path,
			"ip":          c.ClientIP(),
			"user_agent":  c.Request.UserAgent(),
		})

		response.InternalServerError(c, "INTERNAL_ERROR", "An internal server error occurred")

		c.Abort()
	})
}

func TimeoutMiddleware(log *logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		select {
		case <-c.Request.Context().Done():
			ctx := c.Request.Context()
			log.LogWarn(ctx, "Request timeout or cancellation", map[string]interface{}{
				"method": c.Request.Method,
				"path":   c.Request.URL.Path,
				"error":  c.Request.Context().Err().Error(),
			})

			response.ServiceUnavailable(c, "REQUEST_TIMEOUT", "Request timeout")
			c.Abort()
			return
		default:
			c.Next()
		}
	}
}
