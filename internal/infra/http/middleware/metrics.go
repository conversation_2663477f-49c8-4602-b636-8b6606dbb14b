package middleware

import (
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/smooth-inc/backend/internal/infra/logger"
)

type MetricsConfig struct {
	Enabled         bool          `json:"enabled"`
	CollectInterval time.Duration `json:"collect_interval"`
	HistorySize     int           `json:"history_size"`
	EnableDetailed  bool          `json:"enable_detailed"`
}

type Metrics struct {
	TotalRequests       int64                    `json:"total_requests"`
	TotalErrors         int64                    `json:"total_errors"`
	TotalResponseTime   int64                    `json:"total_response_time_ms"`
	RequestsPerSecond   float64                  `json:"requests_per_second"`
	ErrorRate           float64                  `json:"error_rate"`
	AverageResponseTime float64                  `json:"average_response_time_ms"`
	StatusCodes         map[string]int64         `json:"status_codes"`
	Endpoints           map[string]*EndpointStat `json:"endpoints"`
	UserAgents          map[string]int64         `json:"user_agents"`
	IPs                 map[string]int64         `json:"ips"`
	LastUpdated         time.Time                `json:"last_updated"`
	StartTime           time.Time                `json:"start_time"`
}

type EndpointStat struct {
	Count           int64     `json:"count"`
	TotalTime       int64     `json:"total_time_ms"`
	AverageTime     float64   `json:"average_time_ms"`
	ErrorCount      int64     `json:"error_count"`
	LastAccessed    time.Time `json:"last_accessed"`
	MinResponseTime int64     `json:"min_response_time_ms"`
	MaxResponseTime int64     `json:"max_response_time_ms"`
}

type MetricsCollector struct {
	config   MetricsConfig
	metrics  *Metrics
	mutex    sync.RWMutex
	logger   *logger.Logger
	history  []Metrics
	stopChan chan struct{}
}

func NewMetricsCollector(config MetricsConfig, log *logger.Logger) *MetricsCollector {
	collector := &MetricsCollector{
		config: config,
		metrics: &Metrics{
			StatusCodes: make(map[string]int64),
			Endpoints:   make(map[string]*EndpointStat),
			UserAgents:  make(map[string]int64),
			IPs:         make(map[string]int64),
			StartTime:   time.Now(),
			LastUpdated: time.Now(),
		},
		logger:   log,
		history:  make([]Metrics, 0, config.HistorySize),
		stopChan: make(chan struct{}),
	}

	if config.Enabled && config.CollectInterval > 0 {
		go collector.collectRoutine()
	}

	return collector
}

func (mc *MetricsCollector) collectRoutine() {
	ticker := time.NewTicker(mc.config.CollectInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			mc.updateCalculatedMetrics()
		case <-mc.stopChan:
			return
		}
	}
}

func (mc *MetricsCollector) updateCalculatedMetrics() {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	now := time.Now()
	elapsed := now.Sub(mc.metrics.StartTime).Seconds()

	if elapsed > 0 {
		mc.metrics.RequestsPerSecond = float64(mc.metrics.TotalRequests) / elapsed
	}

	if mc.metrics.TotalRequests > 0 {
		mc.metrics.ErrorRate = float64(mc.metrics.TotalErrors) / float64(mc.metrics.TotalRequests) * 100
		mc.metrics.AverageResponseTime = float64(mc.metrics.TotalResponseTime) / float64(mc.metrics.TotalRequests)
	}

	for _, endpoint := range mc.metrics.Endpoints {
		if endpoint.Count > 0 {
			endpoint.AverageTime = float64(endpoint.TotalTime) / float64(endpoint.Count)
		}
	}

	mc.metrics.LastUpdated = now

	if mc.config.HistorySize > 0 {
		snapshot := *mc.metrics
		snapshot.StatusCodes = copyStringInt64Map(mc.metrics.StatusCodes)
		snapshot.UserAgents = copyStringInt64Map(mc.metrics.UserAgents)
		snapshot.IPs = copyStringInt64Map(mc.metrics.IPs)
		snapshot.Endpoints = copyEndpointMap(mc.metrics.Endpoints)

		mc.history = append(mc.history, snapshot)
		if len(mc.history) > mc.config.HistorySize {
			mc.history = mc.history[1:]
		}
	}
}

func (mc *MetricsCollector) RecordRequest(method, path string, statusCode int, duration time.Duration, userAgent, clientIP string) {
	if !mc.config.Enabled {
		return
	}

	atomic.AddInt64(&mc.metrics.TotalRequests, 1)
	atomic.AddInt64(&mc.metrics.TotalResponseTime, duration.Milliseconds())

	if statusCode >= 400 {
		atomic.AddInt64(&mc.metrics.TotalErrors, 1)
	}

	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	statusStr := strconv.Itoa(statusCode)
	mc.metrics.StatusCodes[statusStr]++

	endpointKey := method + " " + path
	endpoint, exists := mc.metrics.Endpoints[endpointKey]
	if !exists {
		endpoint = &EndpointStat{
			MinResponseTime: duration.Milliseconds(),
			MaxResponseTime: duration.Milliseconds(),
		}
		mc.metrics.Endpoints[endpointKey] = endpoint
	}

	endpoint.Count++
	endpoint.TotalTime += duration.Milliseconds()
	endpoint.LastAccessed = time.Now()

	if statusCode >= 400 {
		endpoint.ErrorCount++
	}

	if duration.Milliseconds() < endpoint.MinResponseTime {
		endpoint.MinResponseTime = duration.Milliseconds()
	}
	if duration.Milliseconds() > endpoint.MaxResponseTime {
		endpoint.MaxResponseTime = duration.Milliseconds()
	}

	if mc.config.EnableDetailed {
		if userAgent != "" {
			mc.metrics.UserAgents[userAgent]++
		}
		if clientIP != "" {
			mc.metrics.IPs[clientIP]++
		}
	}
}

func (mc *MetricsCollector) GetMetrics() Metrics {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	result := *mc.metrics
	result.StatusCodes = copyStringInt64Map(mc.metrics.StatusCodes)
	result.UserAgents = copyStringInt64Map(mc.metrics.UserAgents)
	result.IPs = copyStringInt64Map(mc.metrics.IPs)
	result.Endpoints = copyEndpointMap(mc.metrics.Endpoints)

	return result
}

func (mc *MetricsCollector) GetHistory() []Metrics {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	history := make([]Metrics, len(mc.history))
	copy(history, mc.history)
	return history
}

func (mc *MetricsCollector) Reset() {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	atomic.StoreInt64(&mc.metrics.TotalRequests, 0)
	atomic.StoreInt64(&mc.metrics.TotalErrors, 0)
	atomic.StoreInt64(&mc.metrics.TotalResponseTime, 0)

	mc.metrics.RequestsPerSecond = 0
	mc.metrics.ErrorRate = 0
	mc.metrics.AverageResponseTime = 0
	mc.metrics.StatusCodes = make(map[string]int64)
	mc.metrics.Endpoints = make(map[string]*EndpointStat)
	mc.metrics.UserAgents = make(map[string]int64)
	mc.metrics.IPs = make(map[string]int64)
	mc.metrics.StartTime = time.Now()
	mc.metrics.LastUpdated = time.Now()
	mc.history = mc.history[:0]
}

func (mc *MetricsCollector) Stop() {
	close(mc.stopChan)
}

func MetricsMiddleware(collector *MetricsCollector) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		c.Next()

		duration := time.Since(start)
		statusCode := c.Writer.Status()
		userAgent := c.Request.UserAgent()
		clientIP := c.ClientIP()

		collector.RecordRequest(
			c.Request.Method,
			c.Request.URL.Path,
			statusCode,
			duration,
			userAgent,
			clientIP,
		)
	}
}

func copyStringInt64Map(original map[string]int64) map[string]int64 {
	copy := make(map[string]int64)
	for k, v := range original {
		copy[k] = v
	}
	return copy
}

func copyEndpointMap(original map[string]*EndpointStat) map[string]*EndpointStat {
	copy := make(map[string]*EndpointStat)
	for k, v := range original {
		copy[k] = &EndpointStat{
			Count:           v.Count,
			TotalTime:       v.TotalTime,
			AverageTime:     v.AverageTime,
			ErrorCount:      v.ErrorCount,
			LastAccessed:    v.LastAccessed,
			MinResponseTime: v.MinResponseTime,
			MaxResponseTime: v.MaxResponseTime,
		}
	}
	return copy
}

func DefaultMetricsConfig() MetricsConfig {
	return MetricsConfig{
		Enabled:         true,
		CollectInterval: time.Second * 30,
		HistorySize:     100,
		EnableDetailed:  false,
	}
}

func DetailedMetricsConfig() MetricsConfig {
	return MetricsConfig{
		Enabled:         true,
		CollectInterval: time.Second * 10,
		HistorySize:     200,
		EnableDetailed:  true,
	}
}
