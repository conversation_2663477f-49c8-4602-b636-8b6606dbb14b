package middleware

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/smooth-inc/backend/internal/infra/logger"
	apperrors "github.com/smooth-inc/backend/pkg/errors"
)

type ErrorResponse struct {
	Error     ErrorDetail `json:"error"`
	Timestamp time.Time   `json:"timestamp"`
	RequestID string      `json:"request_id,omitempty"`
}

type ErrorDetail struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func ErrorHandlerMiddleware(logger *logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		if len(c.Errors) > 0 {
			err := c.Errors.Last().Err
			requestID := getRequestID(c)

			logger.LogError(c.Request.Context(), err, "Request error occurred", map[string]interface{}{
				"request_id": requestID,
				"method":     c.Request.Method,
				"path":       c.Request.URL.Path,
				"user_agent": c.Request.UserAgent(),
				"ip":         c.Client<PERSON>(),
			})

			if c.Writer.Written() {
				return
			}

			if appErr, ok := apperrors.IsAppError(err); ok {
				c.JSON(appErr.StatusCode, ErrorResponse{
					Error: ErrorDetail{
						Code:    string(appErr.Code),
						Message: appErr.Message,
						Details: appErr.Details,
					},
					Timestamp: time.Now().UTC(),
					RequestID: requestID,
				})
				return
			}

			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error: ErrorDetail{
					Code:    string(apperrors.ErrCodeInternal),
					Message: "An internal error occurred",
				},
				Timestamp: time.Now().UTC(),
				RequestID: requestID,
			})
		}
	}
}

func getRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}
