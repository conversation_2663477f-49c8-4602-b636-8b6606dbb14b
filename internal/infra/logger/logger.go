package logger

import (
	"context"
	"fmt"
	"io"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type Logger struct {
	*logrus.Logger
	serviceName string
	version     string
}

type Config struct {
	Level       string `yaml:"level" env:"LOG_LEVEL" default:"info"`
	Format      string `yaml:"format" env:"LOG_FORMAT" default:"json"` // json or text
	ServiceName string `yaml:"service_name" env:"SERVICE_NAME" default:"smooth-backend"`
	Version     string `yaml:"version" env:"VERSION" default:"1.0.0"`
	Output      io.Writer
}

func New(cfg Config) *Logger {
	logger := logrus.New()

	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	if cfg.Format == "json" {
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339Nano,
			FieldMap: logrus.FieldMap{
				logrus.FieldKeyTime:  "timestamp",
				logrus.FieldKeyLevel: "level",
				logrus.FieldKeyMsg:   "message",
				logrus.FieldKeyFunc:  "caller",
			},
		})
	} else {
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: time.RFC3339,
			ForceColors:     true,
		})
	}

	if cfg.Output != nil {
		logger.SetOutput(cfg.Output)
	} else {
		logger.SetOutput(os.Stdout)
	}

	return &Logger{
		Logger:      logger,
		serviceName: cfg.ServiceName,
		version:     cfg.Version,
	}
}

func (l *Logger) WithContext(ctx context.Context) *logrus.Entry {
	entry := l.WithFields(logrus.Fields{
		"service": l.serviceName,
		"version": l.version,
	})

	if requestID := GetRequestID(ctx); requestID != "" {
		entry = entry.WithField("request_id", requestID)
	}

	if userID := GetUserID(ctx); userID != "" {
		entry = entry.WithField("user_id", userID)
	}

	// TODO: Add OpenTelemetry trace information when available
	// if span := trace.SpanFromContext(ctx); span.SpanContext().IsValid() {
	//     spanCtx := span.SpanContext()
	//     entry = entry.WithFields(logrus.Fields{
	//         "trace_id": spanCtx.TraceID().String(),
	//         "span_id":  spanCtx.SpanID().String(),
	//     })
	// }

	return entry
}

func (l *Logger) WithHTTPRequest(ctx context.Context, c *gin.Context) *logrus.Entry {
	entry := l.WithContext(ctx)

	if c != nil {
		entry = entry.WithFields(logrus.Fields{
			"method":     c.Request.Method,
			"path":       c.Request.URL.Path,
			"query":      c.Request.URL.RawQuery,
			"user_agent": c.Request.UserAgent(),
			"ip":         c.ClientIP(),
		})
	}

	return entry
}

func (l *Logger) LogHTTPRequest(ctx context.Context, c *gin.Context, duration time.Duration, statusCode int, responseSize int64) {
	entry := l.WithHTTPRequest(ctx, c).WithFields(logrus.Fields{
		"status_code":   statusCode,
		"duration_ms":   duration.Milliseconds(),
		"response_size": responseSize,
	})

	message := fmt.Sprintf("%s %s", c.Request.Method, c.Request.URL.Path)

	switch {
	case statusCode >= 500:
		entry.Error(message)
	case statusCode >= 400:
		entry.Warn(message)
	case statusCode >= 300:
		entry.Info(message)
	default:
		entry.Info(message)
	}
}

func (l *Logger) LogError(ctx context.Context, err error, message string, fields ...logrus.Fields) {
	entry := l.WithContext(ctx).WithError(err)

	for _, field := range fields {
		entry = entry.WithFields(field)
	}

	entry.Error(message)
}

func (l *Logger) LogInfo(ctx context.Context, message string, fields ...logrus.Fields) {
	entry := l.WithContext(ctx)

	for _, field := range fields {
		entry = entry.WithFields(field)
	}

	entry.Info(message)
}

func (l *Logger) LogWarn(ctx context.Context, message string, fields ...logrus.Fields) {
	entry := l.WithContext(ctx)

	for _, field := range fields {
		entry = entry.WithFields(field)
	}

	entry.Warn(message)
}

func (l *Logger) LogDebug(ctx context.Context, message string, fields ...logrus.Fields) {
	entry := l.WithContext(ctx)

	for _, field := range fields {
		entry = entry.WithFields(field)
	}

	entry.Debug(message)
}

func GenerateRequestID() string {
	return uuid.New().String()
}
