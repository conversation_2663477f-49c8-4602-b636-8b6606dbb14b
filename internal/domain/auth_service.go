package domain

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net"
	"time"

	"github.com/google/uuid"
)

type AuthService struct{}

func NewAuthService() *AuthService {
	return &AuthService{}
}

func (s *AuthService) GenerateDeviceID(userAgent, ipAddress string) string {
	return fmt.Sprintf("%x", []byte(userAgent+ipAddress))[:16]
}

func (s *AuthService) HashRefreshToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

func (s *AuthService) HashVerificationToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

func (s *AuthService) ValidateLoginRequest(req *LoginRequest, ipAddress, userAgent string) (*LoginData, error) {
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	var ip net.IP
	if ipAddress != "" {
		ip = net.ParseIP(ipAddress)
	}

	deviceID := req.DeviceID
	if deviceID == "" {
		deviceID = s.GenerateDeviceID(userAgent, ipAddress)
	}

	return &LoginData{
		Username:   req.GetUsername(),
		Password:   req.Password,
		DeviceID:   deviceID,
		DeviceName: req.DeviceName,
		DeviceType: req.DeviceType,
		UserAgent:  userAgent,
		IP:         ip,
	}, nil
}

func (s *AuthService) PrepareUserSession(userID uuid.UUID, deviceID, deviceName, deviceType, userAgent string, ip net.IP, refreshToken string, refreshTokenExpiresAt time.Time) (*UserSession, error) {
	return NewUserSession(
		userID,
		deviceID,
		deviceName,
		deviceType,
		userAgent,
		ip,
		refreshToken,
		refreshTokenExpiresAt,
	)
}

type LoginData struct {
	Username   string
	Password   string
	DeviceID   string
	DeviceName string
	DeviceType string
	UserAgent  string
	IP         net.IP
}
