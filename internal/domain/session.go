package domain

import (
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
)

type SessionStatus string

const (
	SessionStatusActive    SessionStatus = "active"
	SessionStatusCompleted SessionStatus = "completed"
	SessionStatusCancelled SessionStatus = "cancelled"
	SessionStatusError     SessionStatus = "error"
)

type Session struct {
	ID                  uuid.UUID
	UserID              *uuid.UUID
	PlateID             *uuid.UUID
	ParkingLotID        uuid.UUID
	EntryTime           time.Time
	ExitTime            *time.Time
	DurationMinutes     *int
	Amount              *int
	DiscountAmount      int
	Status              SessionStatus
	IsPaid              bool
	EntryImageURL       *string
	ExitImageURL        *string
	DetectionConfidence *float64
	ErrorMessage        *string
	ManualOverride      bool
	CreatedAt           time.Time
	UpdatedAt           time.Time
	DeletedAt           *time.Time
}

func NewSession(parkingLotID uuid.UUID, plateID *uuid.UUID, userID *uuid.UUID) (*Session, error) {
	if parkingLotID == uuid.Nil {
		return nil, errors.New("parking lot ID is required")
	}

	return &Session{
		ID:           uuid.New(),
		UserID:       userID,
		PlateID:      plateID,
		ParkingLotID: parkingLotID,
		EntryTime:    time.Now(),
		Status:       SessionStatusActive,
		IsPaid:       false,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}, nil
}

func (s *Session) CompleteSession(exitTime time.Time, finalAmount int) error {
	if s.Status != SessionStatusActive {
		return errors.New("session is not active")
	}

	duration := int(exitTime.Sub(s.EntryTime).Minutes())
	s.ExitTime = &exitTime
	s.DurationMinutes = &duration
	s.Amount = &finalAmount
	s.Status = SessionStatusCompleted
	s.UpdatedAt = time.Now()

	return nil
}

func (s *Session) MarkAsPaid() {
	s.IsPaid = true
	s.UpdatedAt = time.Now()
}

func (s *Session) CancelSession(reason string) {
	s.Status = SessionStatusCancelled
	if reason != "" {
		s.ErrorMessage = &reason
	}
	s.UpdatedAt = time.Now()
}

func (s *Session) SetError(errorMessage string) {
	s.Status = SessionStatusError
	s.ErrorMessage = &errorMessage
	s.UpdatedAt = time.Now()
}

func (s *Session) ApplyDiscount(discountAmount int) error {
	if discountAmount < 0 {
		return errors.New("discount amount cannot be negative")
	}

	s.DiscountAmount = discountAmount
	s.UpdatedAt = time.Now()
	return nil
}

func (s *Session) SetManualOverride() {
	s.ManualOverride = true
	s.UpdatedAt = time.Now()
}

func (s *Session) IsActive() bool {
	return s.Status == SessionStatusActive
}

func (s *Session) CanBeCompleted() bool {
	return s.Status == SessionStatusActive && s.ExitTime == nil
}

func (s *Session) GetFinalAmount() int {
	if s.Amount == nil {
		return 0
	}
	return *s.Amount - s.DiscountAmount
}

func (s *Session) RequiresPayment() bool {
	return s.Status == SessionStatusCompleted && !s.IsPaid && s.GetFinalAmount() > 0
}

func (s *Session) CanProcessAutoPayment() bool {
	return s.RequiresPayment() && s.UserID != nil
}

func (s *Session) GetNotificationTitle() string {
	if s.Status == SessionStatusActive {
		return "Parking Started"
	} else if s.Status == SessionStatusCompleted {
		if s.IsPaid {
			return "Parking Completed & Paid"
		}
		return "Parking Completed - Payment Required"
	}
	return "Parking Status Update"
}

func (s *Session) GetNotificationMessage() string {
	if s.Status == SessionStatusActive {
		return "Your parking session has started. You will be charged when you leave."
	} else if s.Status == SessionStatusCompleted {
		if s.IsPaid {
			return fmt.Sprintf("Your parking session is complete. Total: ¥%d", s.GetFinalAmount())
		}
		return fmt.Sprintf("Your parking session is complete. Please pay ¥%d", s.GetFinalAmount())
	}
	return "Your parking status has been updated."
}
