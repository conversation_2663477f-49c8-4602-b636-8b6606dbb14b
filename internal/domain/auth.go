package domain

import (
	"errors"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

type TokenType string

const (
	TokenTypeAccess  TokenType = "access"
	TokenTypeRefresh TokenType = "refresh"
)

type JWTClaims struct {
	UserID            uuid.UUID    `json:"user_id"`
	Email             string       `json:"email"`
	Username          string       `json:"username"`
	Role              UserRole     `json:"role"`
	PreferredLanguage LanguageCode `json:"preferred_language"`
	TokenType         TokenType    `json:"token_type"`
	SessionID         uuid.UUID    `json:"session_id,omitempty"`
	jwt.RegisteredClaims
}

type TokenPair struct {
	AccessToken           string    `json:"access_token"`
	RefreshToken          string    `json:"refresh_token"`
	AccessTokenExpiresAt  time.Time `json:"access_token_expires_at"`
	RefreshTokenExpiresAt time.Time `json:"refresh_token_expires_at"`
}

type AuthResponse struct {
	User   *User      `json:"user"`
	Tokens *TokenPair `json:"tokens"`
}

type RegisterRequest struct {
	Username          string       `json:"username"`
	Email             string       `json:"email"`
	Password          string       `json:"password"`
	Name              string       `json:"name"`
	Phone             *string      `json:"phone,omitempty"`
	PreferredLanguage LanguageCode `json:"preferred_language"`
}

type LoginRequest struct {
	Identifier string `json:"identifier"` // Username or email
	Password   string `json:"password"`
	DeviceID   string `json:"device_id,omitempty"`
	DeviceName string `json:"device_name,omitempty"`
	DeviceType string `json:"device_type,omitempty"`
}

type EmailVerificationRequest struct {
	Token string `json:"token"`
}

type ResendVerificationRequest struct {
	Email string `json:"email"`
}

type ForgotPasswordRequest struct {
	Email string `json:"email"`
}

type ResetPasswordRequest struct {
	Token    string `json:"token"`
	Password string `json:"password"`
}

type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password"`
	NewPassword     string `json:"new_password"`
}

type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token"`
	DeviceID     string `json:"device_id,omitempty"`
}

type MessageResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
}

type SessionResponse struct {
	ID         uuid.UUID `json:"id"`
	DeviceID   string    `json:"device_id"`
	DeviceName string    `json:"device_name"`
	DeviceType string    `json:"device_type"`
	IPAddress  string    `json:"ip_address"`
	LastUsedAt time.Time `json:"last_used_at"`
	CreatedAt  time.Time `json:"created_at"`
	IsCurrent  bool      `json:"is_current"`
}

type SessionsResponse struct {
	Sessions []SessionResponse `json:"sessions"`
}

func (r *RegisterRequest) Validate() error {
	if err := ValidateUsername(r.Username); err != nil {
		return err
	}
	if err := ValidateEmail(r.Email); err != nil {
		return err
	}
	if err := ValidateName(r.Name); err != nil {
		return err
	}
	if err := ValidatePassword(r.Password); err != nil {
		return err
	}
	if err := ValidateLanguageCode(r.PreferredLanguage); err != nil {
		return err
	}
	if r.Phone != nil && *r.Phone != "" {
		if err := ValidatePhone(*r.Phone); err != nil {
			return err
		}
	}
	return nil
}

func (r *LoginRequest) Validate() error {
	if r.Identifier == "" {
		return errors.New("username or email is required")
	}
	if r.Password == "" {
		return errors.New("password is required")
	}
	if r.DeviceID != "" {
		if err := ValidateDeviceID(r.DeviceID); err != nil {
			return err
		}
	}
	if r.DeviceType != "" {
		if err := ValidateDeviceType(r.DeviceType); err != nil {
			return err
		}
	}
	return nil
}

func (r *LoginRequest) IsEmailLogin() bool {
	return strings.Contains(r.Identifier, "@")
}

func (r *LoginRequest) GetUsername() string {
	if r.IsEmailLogin() {
		return strings.ToLower(r.Identifier)
	}
	return strings.ToLower(r.Identifier)
}

func (r *LoginRequest) GetEmail() string {
	if r.IsEmailLogin() {
		return strings.ToLower(r.Identifier)
	}
	return ""
}

func (r *EmailVerificationRequest) Validate() error {
	if r.Token == "" {
		return errors.New("verification token is required")
	}
	return nil
}

func (r *ResendVerificationRequest) Validate() error {
	return ValidateEmail(r.Email)
}

func (r *ForgotPasswordRequest) Validate() error {
	return ValidateEmail(r.Email)
}

func (r *ResetPasswordRequest) Validate() error {
	if r.Token == "" {
		return errors.New("reset token is required")
	}
	return ValidatePassword(r.Password)
}

func (r *ChangePasswordRequest) Validate() error {
	if r.CurrentPassword == "" {
		return errors.New("current password is required")
	}
	return ValidatePassword(r.NewPassword)
}

func (r *RefreshTokenRequest) Validate() error {
	if r.RefreshToken == "" {
		return errors.New("refresh token is required")
	}
	if r.DeviceID != "" {
		if err := ValidateDeviceID(r.DeviceID); err != nil {
			return err
		}
	}
	return nil
}

func ValidatePhone(phone string) error {
	if len(phone) > 20 {
		return errors.New("phone number must be at most 20 characters long")
	}
	if len(phone) < 10 {
		return errors.New("phone number must be at least 10 characters long")
	}
	return nil
}
