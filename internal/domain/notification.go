package domain

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

type NotificationType string

const (
	NotificationTypeParked           NotificationType = "parked"
	NotificationTypePaymentCompleted NotificationType = "payment_completed"
	NotificationTypeOverstayWarning  NotificationType = "overstay_warning"
	NotificationTypePriceAlert       NotificationType = "price_alert"
	NotificationTypeBookingReminder  NotificationType = "booking_reminder"
)

type Notification struct {
	ID           uuid.UUID
	UserID       uuid.UUID
	Type         NotificationType
	Title        string
	Message      string
	SessionID    *uuid.UUID
	PaymentID    *uuid.UUID
	ParkingLotID *uuid.UUID
	IsSent       bool
	SentAt       *time.Time
	CreatedAt    time.Time
	UpdatedAt    time.Time
	DeletedAt    *time.Time
}

func NewNotification(userID uuid.UUID, notificationType NotificationType, title, message string) (*Notification, error) {
	if userID == uuid.Nil {
		return nil, errors.New("user ID is required")
	}
	if title == "" {
		return nil, errors.New("title is required")
	}
	if message == "" {
		return nil, errors.New("message is required")
	}

	return &Notification{
		ID:        uuid.New(),
		UserID:    userID,
		Type:      notificationType,
		Title:     title,
		Message:   message,
		IsSent:    false,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}, nil
}

func (n *Notification) SetSessionID(sessionID uuid.UUID) {
	n.SessionID = &sessionID
	n.UpdatedAt = time.Now()
}

func (n *Notification) SetPaymentID(paymentID uuid.UUID) {
	n.PaymentID = &paymentID
	n.UpdatedAt = time.Now()
}

func (n *Notification) SetParkingLotID(parkingLotID uuid.UUID) {
	n.ParkingLotID = &parkingLotID
	n.UpdatedAt = time.Now()
}

func (n *Notification) MarkAsSent() {
	n.IsSent = true
	now := time.Now()
	n.SentAt = &now
	n.UpdatedAt = now
}

func (n *Notification) IsUrgent() bool {
	return n.Type == NotificationTypeOverstayWarning
}

func (n *Notification) ShouldRetry() bool {
	if n.IsSent {
		return false
	}

	if n.IsUrgent() {
		return time.Since(n.CreatedAt) < time.Hour
	}

	return time.Since(n.CreatedAt) < 24*time.Hour
}

func (n *Notification) GetDisplayMessage() string {
	if n.IsUrgent() {
		return "🚨 " + n.Message
	}
	return n.Message
}
