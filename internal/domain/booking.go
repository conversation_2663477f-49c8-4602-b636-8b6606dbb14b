package domain

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

type BookingStatus string

const (
	BookingStatusConfirmed BookingStatus = "confirmed"
	BookingStatusCancelled BookingStatus = "cancelled"
	BookingStatusCompleted BookingStatus = "completed"
	BookingStatusNoShow    BookingStatus = "no_show"
)

type Booking struct {
	ID              uuid.UUID
	UserID          uuid.UUID
	PlateID         uuid.UUID
	ParkingLotID    uuid.UUID
	StartTime       time.Time
	EndTime         time.Time
	HourlyRate      int
	TotalAmount     int
	Status          BookingStatus
	CancelledAt     *time.Time
	CancellationFee int
	CreatedAt       time.Time
	UpdatedAt       time.Time
	DeletedAt       *time.Time
}

func NewBooking(userID, plateID, parkingLotID uuid.UUID, startTime, endTime time.Time, hourlyRate int) (*Booking, error) {
	if userID == uuid.Nil {
		return nil, errors.New("user ID is required")
	}
	if plateID == uuid.Nil {
		return nil, errors.New("plate ID is required")
	}
	if parkingLotID == uuid.Nil {
		return nil, errors.New("parking lot ID is required")
	}
	if startTime.After(endTime) {
		return nil, errors.New("start time must be before end time")
	}
	if startTime.Before(time.Now()) {
		return nil, errors.New("start time cannot be in the past")
	}
	if hourlyRate <= 0 {
		return nil, errors.New("hourly rate must be positive")
	}

	duration := endTime.Sub(startTime)
	totalHours := int(duration.Hours())
	if duration.Minutes() > float64(totalHours*60) {
		totalHours++ // Round up partial hours
	}
	totalAmount := totalHours * hourlyRate

	return &Booking{
		ID:           uuid.New(),
		UserID:       userID,
		PlateID:      plateID,
		ParkingLotID: parkingLotID,
		StartTime:    startTime,
		EndTime:      endTime,
		HourlyRate:   hourlyRate,
		TotalAmount:  totalAmount,
		Status:       BookingStatusConfirmed,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}, nil
}

func (b *Booking) Cancel(cancellationFee int) error {
	if b.Status != BookingStatusConfirmed {
		return errors.New("only confirmed bookings can be cancelled")
	}
	if time.Now().After(b.StartTime) {
		return errors.New("cannot cancel booking after start time")
	}

	b.Status = BookingStatusCancelled
	now := time.Now()
	b.CancelledAt = &now
	b.CancellationFee = cancellationFee
	b.UpdatedAt = now

	return nil
}

func (b *Booking) MarkAsCompleted() error {
	if b.Status != BookingStatusConfirmed {
		return errors.New("only confirmed bookings can be completed")
	}

	b.Status = BookingStatusCompleted
	b.UpdatedAt = time.Now()
	return nil
}

func (b *Booking) MarkAsNoShow() error {
	if b.Status != BookingStatusConfirmed {
		return errors.New("only confirmed bookings can be marked as no-show")
	}
	if time.Now().Before(b.EndTime) {
		return errors.New("cannot mark as no-show before end time")
	}

	b.Status = BookingStatusNoShow
	b.UpdatedAt = time.Now()
	return nil
}

func (b *Booking) IsActive() bool {
	now := time.Now()
	return b.Status == BookingStatusConfirmed &&
		now.After(b.StartTime.Add(-15*time.Minute)) && // Allow 15min early
		now.Before(b.EndTime)
}

func (b *Booking) CanBeCancelled() bool {
	return b.Status == BookingStatusConfirmed && time.Now().Before(b.StartTime)
}

func (b *Booking) GetDurationHours() float64 {
	return b.EndTime.Sub(b.StartTime).Hours()
}

func (b *Booking) GetNetAmount() int {
	return b.TotalAmount - b.CancellationFee
}

func (b *Booking) IsOverlapping(startTime, endTime time.Time) bool {
	return b.StartTime.Before(endTime) && b.EndTime.After(startTime)
}
