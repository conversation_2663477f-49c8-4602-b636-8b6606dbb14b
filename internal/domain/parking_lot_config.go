package domain

import (
	"encoding/json"
	"errors"
	"time"

	"github.com/google/uuid"
)

type ParkingLotConfig struct {
	ID             uuid.UUID
	ParkingLotID   uuid.UUID
	ConfigName     string
	IsActive       bool
	PricingRules   PricingRules
	EffectiveFrom  time.Time
	EffectiveUntil *time.Time
	CreatedAt      time.Time
	UpdatedAt      time.Time
	CreatedBy      uuid.UUID
	DeletedAt      *time.Time
}

// PricingRules represents the complete pricing configuration
type PricingRules struct {
	LotID              string          `json:"lot_id"`
	InitialFreeMinutes int             `json:"initial_free_minutes"`
	DailyCap           int             `json:"daily_cap"`
	NightCaps          []NightCap      `json:"night_caps"`
	Overrides          []PriceOverride `json:"overrides"`
	Rules              []PricingRule   `json:"rules"`
}

// NightCap represents a recurring per-night pricing cap
type NightCap struct {
	Start string `json:"start"` // "22:00"
	End   string `json:"end"`   // "08:00"
	Cap   int    `json:"cap"`   // Maximum fee for this night window
}

// PriceOverride represents absolute date-range pricing overrides
type PriceOverride struct {
	Name         string `json:"name"`
	Start        string `json:"start"` // "2025-01-01T00:00"
	End          string `json:"end"`   // "2025-01-03T23:59"
	UnitMinutes  int    `json:"unit_minutes"`
	PricePerUnit int    `json:"price_per_unit"`
}

// PricingRule represents weekday and time-band pricing rules
type PricingRule struct {
	Days         []string `json:"days"`           // ["Mon","Tue","Wed","Thu","Fri"]
	Start        string   `json:"start"`          // "15:00"
	End          string   `json:"end"`            // "17:00"
	UnitMinutes  int      `json:"unit_minutes"`   // 10
	PricePerUnit int      `json:"price_per_unit"` // 100
}

func NewParkingLotConfig(parkingLotID uuid.UUID, configName string, pricingRules PricingRules, createdBy uuid.UUID) (*ParkingLotConfig, error) {
	if parkingLotID == uuid.Nil {
		return nil, errors.New("parking lot ID is required")
	}
	if configName == "" {
		return nil, errors.New("config name is required")
	}
	if createdBy == uuid.Nil {
		return nil, errors.New("created by user ID is required")
	}

	// Validate pricing rules
	if err := validatePricingRules(pricingRules); err != nil {
		return nil, err
	}

	return &ParkingLotConfig{
		ID:            uuid.New(),
		ParkingLotID:  parkingLotID,
		ConfigName:    configName,
		IsActive:      false, // New configs start as inactive
		PricingRules:  pricingRules,
		EffectiveFrom: time.Now(),
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
		CreatedBy:     createdBy,
	}, nil
}

func (c *ParkingLotConfig) Activate(effectiveFrom time.Time, effectiveUntil *time.Time) error {
	if effectiveFrom.Before(time.Now()) {
		return errors.New("effective from date cannot be in the past")
	}
	if effectiveUntil != nil && effectiveUntil.Before(effectiveFrom) {
		return errors.New("effective until date must be after effective from date")
	}

	c.IsActive = true
	c.EffectiveFrom = effectiveFrom
	c.EffectiveUntil = effectiveUntil
	c.UpdatedAt = time.Now()
	return nil
}

func (c *ParkingLotConfig) Deactivate() {
	c.IsActive = false
	c.UpdatedAt = time.Now()
}

func (c *ParkingLotConfig) UpdatePricingRules(pricingRules PricingRules) error {
	if err := validatePricingRules(pricingRules); err != nil {
		return err
	}

	c.PricingRules = pricingRules
	c.UpdatedAt = time.Now()
	return nil
}

func (c *ParkingLotConfig) IsEffectiveAt(t time.Time) bool {
	if !c.IsActive {
		return false
	}
	if t.Before(c.EffectiveFrom) {
		return false
	}
	if c.EffectiveUntil != nil && t.After(*c.EffectiveUntil) {
		return false
	}
	return true
}

func (c *ParkingLotConfig) ToJSON() (string, error) {
	data, err := json.Marshal(c.PricingRules)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

func (c *ParkingLotConfig) FromJSON(jsonStr string) error {
	var rules PricingRules
	if err := json.Unmarshal([]byte(jsonStr), &rules); err != nil {
		return err
	}

	if err := validatePricingRules(rules); err != nil {
		return err
	}

	c.PricingRules = rules
	c.UpdatedAt = time.Now()
	return nil
}

func validatePricingRules(rules PricingRules) error {
	if rules.LotID == "" {
		return errors.New("lot_id is required in pricing rules")
	}
	if rules.InitialFreeMinutes < 0 {
		return errors.New("initial_free_minutes cannot be negative")
	}
	if rules.DailyCap < 0 {
		return errors.New("daily_cap cannot be negative")
	}

	for i, cap := range rules.NightCaps {
		if cap.Start == "" || cap.End == "" {
			return errors.New("night cap start and end times are required")
		}
		if cap.Cap < 0 {
			return errors.New("night cap amount cannot be negative")
		}
		// TODO: Add time format validation
		_ = i
	}

	for i, override := range rules.Overrides {
		if override.Name == "" {
			return errors.New("override name is required")
		}
		if override.Start == "" || override.End == "" {
			return errors.New("override start and end times are required")
		}
		if override.UnitMinutes <= 0 {
			return errors.New("override unit_minutes must be positive")
		}
		if override.PricePerUnit < 0 {
			return errors.New("override price_per_unit cannot be negative")
		}
		// TODO: Add datetime format validation
		_ = i
	}

	for i, rule := range rules.Rules {
		if len(rule.Days) == 0 {
			return errors.New("pricing rule days are required")
		}
		if rule.Start == "" || rule.End == "" {
			return errors.New("pricing rule start and end times are required")
		}
		if rule.UnitMinutes <= 0 {
			return errors.New("pricing rule unit_minutes must be positive")
		}
		if rule.PricePerUnit < 0 {
			return errors.New("pricing rule price_per_unit cannot be negative")
		}
		// TODO: Add time format and day validation
		_ = i
	}

	return nil
}

func CreateDefaultPricingRules(lotID string) PricingRules {
	return PricingRules{
		LotID:              lotID,
		InitialFreeMinutes: 30,
		DailyCap:           2000,
		NightCaps: []NightCap{
			{
				Start: "22:00",
				End:   "08:00",
				Cap:   800,
			},
		},
		Overrides: []PriceOverride{},
		Rules: []PricingRule{
			{
				Days:         []string{"Mon", "Tue", "Wed", "Thu", "Fri"},
				Start:        "08:00",
				End:          "18:00",
				UnitMinutes:  30,
				PricePerUnit: 200,
			},
			{
				Days:         []string{"Mon", "Tue", "Wed", "Thu", "Fri"},
				Start:        "18:00",
				End:          "22:00",
				UnitMinutes:  60,
				PricePerUnit: 150,
			},
			{
				Days:         []string{"Sat", "Sun"},
				Start:        "00:00",
				End:          "24:00",
				UnitMinutes:  60,
				PricePerUnit: 300,
			},
		},
	}
}
