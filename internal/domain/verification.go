package domain

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
)

type VerificationType string

const (
	VerificationTypeEmailVerification VerificationType = "email_verification"
	VerificationTypePasswordReset     VerificationType = "password_reset"
)

type VerificationToken struct {
	ID        uuid.UUID
	UserID    uuid.UUID
	TokenHash string
	Type      VerificationType
	Email     string
	ExpiresAt time.Time
	UsedAt    *time.Time
	CreatedAt time.Time
}

type VerificationTokenData struct {
	Token     string
	UserID    uuid.UUID
	Email     string
	Type      VerificationType
	CreatedAt time.Time
	ExpiresAt time.Time
}

type EmailVerificationData struct {
	UserID    uuid.UUID
	Email     string
	Name      string
	Language  LanguageCode
	ExpiresAt time.Time
}

type PasswordResetData struct {
	UserID    uuid.UUID
	Email     string
	Name      string
	Language  LanguageCode
	ExpiresAt time.Time
}

func NewEmailVerificationToken(userID uuid.UUID, email string) (*VerificationTokenData, error) {
	token, err := generateSecureToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	return &VerificationTokenData{
		Token:     token,
		UserID:    userID,
		Email:     email,
		Type:      VerificationTypeEmailVerification,
		CreatedAt: time.Now(),
		ExpiresAt: time.Now().Add(24 * time.Hour), // 24 hours validity
	}, nil
}

func NewPasswordResetToken(userID uuid.UUID, email string) (*VerificationTokenData, error) {
	token, err := generateSecureToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	return &VerificationTokenData{
		Token:     token,
		UserID:    userID,
		Email:     email,
		Type:      VerificationTypePasswordReset,
		CreatedAt: time.Now(),
		ExpiresAt: time.Now().Add(1 * time.Hour), // 1 hour validity
	}, nil
}

func (vtd *VerificationTokenData) ToStorageModel() *VerificationToken {
	return &VerificationToken{
		ID:        uuid.New(),
		UserID:    vtd.UserID,
		TokenHash: hashToken(vtd.Token),
		Type:      vtd.Type,
		Email:     vtd.Email,
		ExpiresAt: vtd.ExpiresAt,
		CreatedAt: vtd.CreatedAt,
	}
}

func (vt *VerificationToken) IsValid() bool {
	return vt.UsedAt == nil && time.Now().Before(vt.ExpiresAt)
}

func (vt *VerificationToken) IsExpired() bool {
	return time.Now().After(vt.ExpiresAt)
}

func (vt *VerificationToken) MarkAsUsed() error {
	if !vt.IsValid() {
		return errors.New("token is already used or expired")
	}
	now := time.Now()
	vt.UsedAt = &now
	return nil
}

func (vt *VerificationToken) ValidateToken(token string) bool {
	return vt.TokenHash == hashToken(token)
}

func (vt *VerificationToken) TimeUntilExpiry() time.Duration {
	if vt.IsExpired() {
		return 0
	}
	return time.Until(vt.ExpiresAt)
}

func generateSecureToken() (string, error) {
	bytes := make([]byte, 32) // 256 bits
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

func hashToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

type TokenValidationError struct {
	Type    string
	Message string
}

func (e *TokenValidationError) Error() string {
	return e.Message
}

func NewTokenExpiredError() *TokenValidationError {
	return &TokenValidationError{
		Type:    "TOKEN_EXPIRED",
		Message: "verification token has expired",
	}
}

func NewTokenUsedError() *TokenValidationError {
	return &TokenValidationError{
		Type:    "TOKEN_USED",
		Message: "verification token has already been used",
	}
}

func NewTokenNotFoundError() *TokenValidationError {
	return &TokenValidationError{
		Type:    "TOKEN_NOT_FOUND",
		Message: "verification token not found or invalid",
	}
}

func NewTokenInvalidError() *TokenValidationError {
	return &TokenValidationError{
		Type:    "TOKEN_INVALID",
		Message: "verification token is invalid",
	}
}
