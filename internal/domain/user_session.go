package domain

import (
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"net"
	"time"

	"github.com/google/uuid"
)

type UserSession struct {
	ID               uuid.UUID
	UserID           uuid.UUID
	DeviceID         string
	DeviceName       string
	DeviceType       string
	IPAddress        net.IP
	UserAgent        string
	RefreshTokenHash string
	ExpiresAt        time.Time
	LastUsedAt       time.Time
	CreatedAt        time.Time
	UpdatedAt        time.Time
}

func NewUserSession(userID uuid.UUID, deviceID, deviceName, deviceType, userAgent string, ipAddress net.IP, refreshToken string, expiresAt time.Time) (*UserSession, error) {
	if userID == uuid.Nil {
		return nil, errors.New("user ID is required")
	}
	if deviceID == "" {
		return nil, errors.New("device ID is required")
	}
	if refreshToken == "" {
		return nil, errors.New("refresh token is required")
	}
	if expiresAt.Before(time.Now()) {
		return nil, errors.New("expiration time must be in the future")
	}

	refreshTokenHash := hashRefreshToken(refreshToken)
	now := time.Now()

	return &UserSession{
		ID:               uuid.New(),
		UserID:           userID,
		DeviceID:         deviceID,
		DeviceName:       deviceName,
		DeviceType:       deviceType,
		IPAddress:        ipAddress,
		UserAgent:        userAgent,
		RefreshTokenHash: refreshTokenHash,
		ExpiresAt:        expiresAt,
		LastUsedAt:       now,
		CreatedAt:        now,
		UpdatedAt:        now,
	}, nil
}

func (s *UserSession) IsExpired() bool {
	return time.Now().After(s.ExpiresAt)
}

func (s *UserSession) IsValid() bool {
	return !s.IsExpired()
}

func (s *UserSession) ValidateRefreshToken(refreshToken string) bool {
	return s.RefreshTokenHash == hashRefreshToken(refreshToken)
}

func (s *UserSession) UpdateLastUsed() {
	s.LastUsedAt = time.Now()
	s.UpdatedAt = time.Now()
}

func (s *UserSession) UpdateRefreshToken(refreshToken string, expiresAt time.Time) error {
	if refreshToken == "" {
		return errors.New("refresh token is required")
	}
	if expiresAt.Before(time.Now()) {
		return errors.New("expiration time must be in the future")
	}

	s.RefreshTokenHash = hashRefreshToken(refreshToken)
	s.ExpiresAt = expiresAt
	s.UpdatedAt = time.Now()
	return nil
}

func (s *UserSession) UpdateDeviceInfo(deviceName, deviceType, userAgent string, ipAddress net.IP) {
	if deviceName != "" {
		s.DeviceName = deviceName
	}
	if deviceType != "" {
		s.DeviceType = deviceType
	}
	if userAgent != "" {
		s.UserAgent = userAgent
	}
	if ipAddress != nil {
		s.IPAddress = ipAddress
	}
	s.UpdatedAt = time.Now()
}

func (s *UserSession) ToSessionResponse(isCurrent bool) *SessionResponse {
	return &SessionResponse{
		ID:         s.ID,
		DeviceID:   s.DeviceID,
		DeviceName: s.DeviceName,
		DeviceType: s.DeviceType,
		IPAddress:  s.IPAddress.String(),
		LastUsedAt: s.LastUsedAt,
		CreatedAt:  s.CreatedAt,
		IsCurrent:  isCurrent,
	}
}

func hashRefreshToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

func ValidateDeviceID(deviceID string) error {
	if deviceID == "" {
		return errors.New("device ID is required")
	}
	if len(deviceID) > 255 {
		return errors.New("device ID must be at most 255 characters long")
	}
	return nil
}

func ValidateDeviceType(deviceType string) error {
	validTypes := map[string]bool{
		"mobile":  true,
		"desktop": true,
		"tablet":  true,
		"web":     true,
		"unknown": true,
	}

	if deviceType != "" && !validTypes[deviceType] {
		return errors.New("invalid device type")
	}
	return nil
}
