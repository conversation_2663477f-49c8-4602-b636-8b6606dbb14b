package domain

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

type LotStatus string

const (
	LotStatusActive      LotStatus = "active"
	LotStatusMaintenance LotStatus = "maintenance"
	LotStatusClosed      LotStatus = "closed"
)

type ParkingLot struct {
	ID            uuid.UUID
	Name          string
	Address       string
	Latitude      float64
	Longitude     float64
	TotalSpots    int
	HeightLimitCm *int
	HourlyRate    int
	DailyMaxRate  *int
	FreeMinutes   int
	Is24h         bool
	OpenTime      *string
	CloseTime     *string
	Features      []string
	Images        []string
	Status        LotStatus
	OperatorName  *string
	ContactPhone  *string
	CreatedAt     time.Time
	UpdatedAt     time.Time
	DeletedAt     *time.Time
}

func NewParkingLot(name, address string, latitude, longitude float64, totalSpots, hourlyRate int) (*ParkingLot, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if address == "" {
		return nil, errors.New("address is required")
	}
	if totalSpots <= 0 {
		return nil, errors.New("total spots must be positive")
	}
	if hourlyRate <= 0 {
		return nil, errors.New("hourly rate must be positive")
	}
	if latitude < -90 || latitude > 90 {
		return nil, errors.New("invalid latitude")
	}
	if longitude < -180 || longitude > 180 {
		return nil, errors.New("invalid longitude")
	}

	return &ParkingLot{
		ID:          uuid.New(),
		Name:        name,
		Address:     address,
		Latitude:    latitude,
		Longitude:   longitude,
		TotalSpots:  totalSpots,
		HourlyRate:  hourlyRate,
		FreeMinutes: 30,
		Is24h:       true,
		Features:    []string{},
		Images:      []string{},
		Status:      LotStatusActive,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}, nil
}

func (p *ParkingLot) SetMaintenance() {
	p.Status = LotStatusMaintenance
	p.UpdatedAt = time.Now()
}

func (p *ParkingLot) SetActive() {
	p.Status = LotStatusActive
	p.UpdatedAt = time.Now()
}

func (p *ParkingLot) SetClosed() {
	p.Status = LotStatusClosed
	p.UpdatedAt = time.Now()
}

func (p *ParkingLot) SetOperatingHours(openTime, closeTime string) error {
	if openTime == "" || closeTime == "" {
		return errors.New("both open and close time must be provided")
	}

	p.Is24h = false
	p.OpenTime = &openTime
	p.CloseTime = &closeTime
	p.UpdatedAt = time.Now()
	return nil
}

func (p *ParkingLot) Set24Hour() {
	p.Is24h = true
	p.OpenTime = nil
	p.CloseTime = nil
	p.UpdatedAt = time.Now()
}

func (p *ParkingLot) AddFeature(feature string) {
	if feature != "" {
		for _, existingFeature := range p.Features {
			if existingFeature == feature {
				return // Feature already exists
			}
		}
		p.Features = append(p.Features, feature)
		p.UpdatedAt = time.Now()
	}
}

func (p *ParkingLot) RemoveFeature(feature string) {
	for i, existingFeature := range p.Features {
		if existingFeature == feature {
			p.Features = append(p.Features[:i], p.Features[i+1:]...)
			p.UpdatedAt = time.Now()
			break
		}
	}
}

func (p *ParkingLot) AddImage(imageURL string) {
	if imageURL != "" {
		p.Images = append(p.Images, imageURL)
		p.UpdatedAt = time.Now()
	}
}

func (p *ParkingLot) SetDailyMaxRate(rate int) error {
	if rate <= 0 {
		return errors.New("daily max rate must be positive")
	}

	p.DailyMaxRate = &rate
	p.UpdatedAt = time.Now()
	return nil
}

func (p *ParkingLot) SetHeightLimit(heightCm int) error {
	if heightCm <= 0 {
		return errors.New("height limit must be positive")
	}

	p.HeightLimitCm = &heightCm
	p.UpdatedAt = time.Now()
	return nil
}

func (p *ParkingLot) IsOpen() bool {
	if p.Status != LotStatusActive {
		return false
	}

	if p.Is24h {
		return true
	}

	return p.OpenTime != nil && p.CloseTime != nil
}

func (p *ParkingLot) IsAcceptingVehicles() bool {
	return p.Status == LotStatusActive && p.IsOpen()
}

func (p *ParkingLot) CalculateRate(durationMinutes int) int {
	if durationMinutes <= p.FreeMinutes {
		return 0
	}

	totalHours := (durationMinutes + 59) / 60
	totalCost := totalHours * p.HourlyRate

	if p.DailyMaxRate != nil && totalCost > *p.DailyMaxRate {
		return *p.DailyMaxRate
	}

	return totalCost
}

func (p *ParkingLot) HasFeature(feature string) bool {
	for _, existingFeature := range p.Features {
		if existingFeature == feature {
			return true
		}
	}
	return false
}
