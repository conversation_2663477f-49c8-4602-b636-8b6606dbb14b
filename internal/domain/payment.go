package domain

import (
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
)

type PaymentStatus string

const (
	PaymentStatusPending    PaymentStatus = "pending"
	PaymentStatusProcessing PaymentStatus = "processing"
	PaymentStatusCompleted  PaymentStatus = "completed"
	PaymentStatusFailed     PaymentStatus = "failed"
	PaymentStatusRefunded   PaymentStatus = "refunded"
)

type Payment struct {
	ID                    uuid.UUID
	SessionID             uuid.UUID
	UserID                uuid.UUID
	Amount                int
	Currency              string
	StripePaymentIntentID *string
	StripePaymentLinkID   *string
	StripeStatus          *string
	PaymentMethodType     *string
	CardLast4             *string
	CardBrand             *string
	Status                PaymentStatus
	PaidAt                *time.Time
	ReceiptURL            *string
	InvoiceNumber         *string
	FailureReason         *string
	RetryCount            int
	CreatedAt             time.Time
	UpdatedAt             time.Time
	DeletedAt             *time.Time
}

func NewPayment(sessionID, userID uuid.UUID, amount int) (*Payment, error) {
	if sessionID == uuid.Nil {
		return nil, errors.New("session ID is required")
	}
	if userID == uuid.Nil {
		return nil, errors.New("user ID is required")
	}
	if amount <= 0 {
		return nil, errors.New("amount must be positive")
	}

	return &Payment{
		ID:        uuid.New(),
		SessionID: sessionID,
		UserID:    userID,
		Amount:    amount,
		Currency:  "JPY",
		Status:    PaymentStatusPending,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}, nil
}

func (p *Payment) MarkAsProcessing() {
	p.Status = PaymentStatusProcessing
	p.UpdatedAt = time.Now()
}

func (p *Payment) MarkAsCompleted() {
	p.Status = PaymentStatusCompleted
	now := time.Now()
	p.PaidAt = &now
	p.UpdatedAt = now
}

func (p *Payment) MarkAsFailed(reason string) {
	p.Status = PaymentStatusFailed
	p.FailureReason = &reason
	p.RetryCount++
	p.UpdatedAt = time.Now()
}

func (p *Payment) MarkAsRefunded() {
	p.Status = PaymentStatusRefunded
	p.UpdatedAt = time.Now()
}

func (p *Payment) SetStripePaymentIntentID(intentID string) {
	p.StripePaymentIntentID = &intentID
	p.UpdatedAt = time.Now()
}

func (p *Payment) SetStripePaymentLinkID(linkID string) {
	p.StripePaymentLinkID = &linkID
	p.UpdatedAt = time.Now()
}

func (p *Payment) SetStripeStatus(status string) {
	p.StripeStatus = &status
	p.UpdatedAt = time.Now()
}

func (p *Payment) SetPaymentMethod(methodType, cardLast4, cardBrand string) {
	p.PaymentMethodType = &methodType
	p.CardLast4 = &cardLast4
	p.CardBrand = &cardBrand
	p.UpdatedAt = time.Now()
}

func (p *Payment) SetReceiptURL(url string) {
	p.ReceiptURL = &url
	p.UpdatedAt = time.Now()
}

func (p *Payment) SetInvoiceNumber(invoiceNumber string) {
	p.InvoiceNumber = &invoiceNumber
	p.UpdatedAt = time.Now()
}

func (p *Payment) IsCompleted() bool {
	return p.Status == PaymentStatusCompleted
}

func (p *Payment) IsPending() bool {
	return p.Status == PaymentStatusPending
}

func (p *Payment) IsFailed() bool {
	return p.Status == PaymentStatusFailed
}

func (p *Payment) CanRetry() bool {
	return p.Status == PaymentStatusFailed && p.RetryCount < 3
}

func (p *Payment) ProcessChargeSucceeded(receiptURL *string, paymentMethodDetails map[string]interface{}) {
	p.MarkAsCompleted()

	if receiptURL != nil {
		p.SetReceiptURL(*receiptURL)
	}

	if card, ok := paymentMethodDetails["card"].(map[string]interface{}); ok {
		if brand, ok := card["brand"].(string); ok {
			if last4, ok := card["last4"].(string); ok {
				p.SetPaymentMethod("card", last4, brand)
			}
		}
	}
}

func (p *Payment) ProcessChargeFailed(failureCode, failureMessage *string) {
	reason := "Charge failed"
	if failureMessage != nil && *failureMessage != "" {
		reason = *failureMessage
	} else if failureCode != nil && *failureCode != "" {
		reason = *failureCode
	}
	p.MarkAsFailed(reason)
}

func (p *Payment) ProcessPaymentIntentSucceeded(receiptURL *string) {
	p.MarkAsCompleted()
	if receiptURL != nil {
		p.SetReceiptURL(*receiptURL)
	}
}

func (p *Payment) ProcessPaymentIntentFailed(lastPaymentError map[string]interface{}) {
	reason := "Payment failed"
	if message, ok := lastPaymentError["message"].(string); ok && message != "" {
		reason = message
	}
	p.MarkAsFailed(reason)
}

func (p *Payment) ProcessPaymentIntentCanceled() {
	p.MarkAsFailed("Payment intent canceled")
}

func (p *Payment) ProcessPaymentIntentRequiresAction() {
	p.SetStripeStatus("requires_action")
}

type DisputeStatus string

const (
	DisputeStatusWarningNeedsResponse DisputeStatus = "warning_needs_response"
	DisputeStatusWarningUnderReview   DisputeStatus = "warning_under_review"
	DisputeStatusWarningClosed        DisputeStatus = "warning_closed"
	DisputeStatusNeedsResponse        DisputeStatus = "needs_response"
	DisputeStatusUnderReview          DisputeStatus = "under_review"
	DisputeStatusChargeRefunded       DisputeStatus = "charge_refunded"
	DisputeStatusWon                  DisputeStatus = "won"
	DisputeStatusLost                 DisputeStatus = "lost"
)

type DisputeReason string

const (
	DisputeReasonDuplicate                DisputeReason = "duplicate"
	DisputeReasonFraudulent               DisputeReason = "fraudulent"
	DisputeReasonSubscriptionCanceled     DisputeReason = "subscription_canceled"
	DisputeReasonProductUnacceptable      DisputeReason = "product_unacceptable"
	DisputeReasonProductNotReceived       DisputeReason = "product_not_received"
	DisputeReasonUnrecognized             DisputeReason = "unrecognized"
	DisputeReasonCreditNotProcessed       DisputeReason = "credit_not_processed"
	DisputeReasonGeneral                  DisputeReason = "general"
	DisputeReasonIncorrectAccountDetails  DisputeReason = "incorrect_account_details"
	DisputeReasonInsufficientFunds        DisputeReason = "insufficient_funds"
	DisputeReasonBankCannotProcess        DisputeReason = "bank_cannot_process"
	DisputeReasonDebitNotAuthorized       DisputeReason = "debit_not_authorized"
	DisputeReasonCustomerInitiated        DisputeReason = "customer_initiated"
)

type Dispute struct {
	ID                    uuid.UUID
	StripeDisputeID       string
	StripeChargeID        string
	PaymentID             *uuid.UUID
	Amount                int
	Currency              string
	Reason                DisputeReason
	Status                DisputeStatus
	EvidenceDueBy         *time.Time
	IsChargeRefundable    bool
	LiveMode              bool
	Metadata              map[string]string
	NetworkReasonCode     *string
	CreatedAt             time.Time
	UpdatedAt             time.Time
}

func NewDispute(stripeDisputeID, stripeChargeID string, amount int, currency string, reason DisputeReason, status DisputeStatus) (*Dispute, error) {
	if stripeDisputeID == "" {
		return nil, errors.New("stripe dispute ID is required")
	}
	if stripeChargeID == "" {
		return nil, errors.New("stripe charge ID is required")
	}
	if amount <= 0 {
		return nil, errors.New("amount must be positive")
	}

	return &Dispute{
		ID:              uuid.New(),
		StripeDisputeID: stripeDisputeID,
		StripeChargeID:  stripeChargeID,
		Amount:          amount,
		Currency:        currency,
		Reason:          reason,
		Status:          status,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}, nil
}

func (d *Dispute) UpdateStatus(status DisputeStatus) {
	d.Status = status
	d.UpdatedAt = time.Now()
}

func (d *Dispute) SetPaymentID(paymentID uuid.UUID) {
	d.PaymentID = &paymentID
	d.UpdatedAt = time.Now()
}

func (d *Dispute) SetEvidenceDueBy(dueBy time.Time) {
	d.EvidenceDueBy = &dueBy
	d.UpdatedAt = time.Now()
}

func (d *Dispute) IsActive() bool {
	return d.Status == DisputeStatusNeedsResponse || d.Status == DisputeStatusUnderReview
}

type InvoiceStatus string

const (
	InvoiceStatusDraft         InvoiceStatus = "draft"
	InvoiceStatusOpen          InvoiceStatus = "open"
	InvoiceStatusPaid          InvoiceStatus = "paid"
	InvoiceStatusUncollectible InvoiceStatus = "uncollectible"
	InvoiceStatusVoid          InvoiceStatus = "void"
)

type Invoice struct {
	ID                  uuid.UUID
	StripeInvoiceID     string
	StripeCustomerID    string
	UserID              *uuid.UUID
	Amount              int
	Currency            string
	Status              InvoiceStatus
	Description         *string
	HostedInvoiceURL    *string
	InvoicePDF          *string
	PaymentIntentID     *string
	SubscriptionID      *string
	AttemptCount        int
	Attempted           bool
	AutoAdvance         bool
	BillingReason       *string
	DueDate             *time.Time
	PaidAt              *time.Time
	CreatedAt           time.Time
	UpdatedAt           time.Time
}

func NewInvoice(stripeInvoiceID, stripeCustomerID string, amount int, currency string, status InvoiceStatus) (*Invoice, error) {
	if stripeInvoiceID == "" {
		return nil, errors.New("stripe invoice ID is required")
	}
	if stripeCustomerID == "" {
		return nil, errors.New("stripe customer ID is required")
	}
	if amount < 0 {
		return nil, errors.New("amount cannot be negative")
	}

	return &Invoice{
		ID:               uuid.New(),
		StripeInvoiceID:  stripeInvoiceID,
		StripeCustomerID: stripeCustomerID,
		Amount:           amount,
		Currency:         currency,
		Status:           status,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}, nil
}

func (i *Invoice) MarkAsPaid(paidAt time.Time) {
	i.Status = InvoiceStatusPaid
	i.PaidAt = &paidAt
	i.UpdatedAt = time.Now()
}

func (i *Invoice) MarkAsFailed() {
	i.Status = InvoiceStatusUncollectible
	i.UpdatedAt = time.Now()
}

func (i *Invoice) SetUserID(userID uuid.UUID) {
	i.UserID = &userID
	i.UpdatedAt = time.Now()
}

func (i *Invoice) SetPaymentIntentID(paymentIntentID string) {
	i.PaymentIntentID = &paymentIntentID
	i.UpdatedAt = time.Now()
}

func (i *Invoice) IsPaid() bool {
	return i.Status == InvoiceStatusPaid
}

func (p *Payment) GetAmountInCurrency() string {
	if p.Currency == "JPY" {
		return fmt.Sprintf("¥%d", p.Amount)
	}
	return fmt.Sprintf("%d %s", p.Amount, p.Currency)
}
