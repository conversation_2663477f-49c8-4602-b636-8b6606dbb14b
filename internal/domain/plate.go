package domain

import (
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
)

type PlateType string

const (
	PlateTypeNormal     PlateType = "normal"
	PlateTypeCommercial PlateType = "commercial"
	PlateTypeRental     PlateType = "rental"
	PlateTypeMilitary   PlateType = "military"
)

type Plate struct {
	ID             uuid.UUID
	UserID         uuid.UUID
	Region         string
	Classification string
	Hiragana       string
	SerialNumber   string
	PlateNumber    string
	PlateType      PlateType
	IsActive       bool
	CreatedAt      time.Time
	UpdatedAt      time.Time
	DeletedAt      *time.Time
}

func NewPlate(userID uuid.UUID, region, classification, hiragana, serialNumber string, plateType PlateType) (*Plate, error) {
	if userID == uuid.Nil {
		return nil, errors.New("user ID is required")
	}
	if region == "" {
		return nil, errors.New("region is required")
	}
	if classification == "" {
		return nil, errors.New("classification is required")
	}
	if hiragana == "" {
		return nil, errors.New("hiragana is required")
	}
	if serialNumber == "" {
		return nil, errors.New("serial number is required")
	}

	plateNumber := fmt.Sprintf("%s%s%s%s", region, classification, hiragana, serialNumber)

	return &Plate{
		ID:             uuid.New(),
		UserID:         userID,
		Region:         region,
		Classification: classification,
		Hiragana:       hiragana,
		SerialNumber:   serialNumber,
		PlateNumber:    plateNumber,
		PlateType:      plateType,
		IsActive:       true,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}, nil
}

func (p *Plate) Deactivate() {
	p.IsActive = false
	p.UpdatedAt = time.Now()
}

func (p *Plate) Activate() {
	p.IsActive = true
	p.UpdatedAt = time.Now()
}

func (p *Plate) IsValid() bool {
	return p.Region != "" &&
		p.Classification != "" &&
		p.Hiragana != "" &&
		p.SerialNumber != "" &&
		p.PlateNumber != ""
}

func (p *Plate) CanBeUsedByUser(userID uuid.UUID) bool {
	return p.UserID == userID && p.IsActive
}
