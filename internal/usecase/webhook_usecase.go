package usecase

import (
	"context"

	"github.com/smooth-inc/backend/internal/gateway/stripe"
	"github.com/smooth-inc/backend/pkg/errors"
)

type webhookUsecase struct {
	stripeGateway     stripe.Gateway
	paymentUC         PaymentUsecase
	paymentMethodUC   PaymentMethodUsecase
}



func NewWebhookUsecase(
	stripeGateway stripe.Gateway,
	paymentUC PaymentUsecase,
	paymentMethodUC PaymentMethodUsecase,
) WebhookUsecase {
	return &webhookUsecase{
		stripeGateway:   stripeGateway,
		paymentUC:       paymentUC,
		paymentMethodUC: paymentMethodUC,
	}
}

func (uc *webhookUsecase) ProcessStripeWebhook(ctx context.Context, payload []byte, signature string) error {
	event, err := uc.stripeGateway.VerifyWebhook(ctx, payload, signature)
	if err != nil {
		return errors.NewBadRequestError("invalid webhook signature")
	}

	switch event.Type {
	// Payment-related events
	case "charge.dispute.created", "charge.failed", "charge.succeeded",
		 "invoice.payment_failed", "invoice.payment_succeeded",
		 "payment_intent.canceled", "payment_intent.payment_failed", "payment_intent.requires_action", "payment_intent.succeeded":
		return uc.paymentUC.ProcessWebhook(ctx, event.Type, event.Data)

	// Payment method and customer-related events
	case "payment_method.attached", "payment_method.detached", "payment_method.updated",
		 "setup_intent.canceled", "setup_intent.requires_action", "setup_intent.setup_failed", "setup_intent.succeeded",
		 "customer.created", "customer.deleted", "customer.updated":
		return uc.paymentMethodUC.ProcessWebhook(ctx, event.Type, event.Data)

	default:
		// Unknown event type - log and ignore
		return nil
	}
}
