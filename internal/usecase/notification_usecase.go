package usecase

import (
	"context"
	"fmt"

	"github.com/google/uuid"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/repository"
	"github.com/smooth-inc/backend/pkg/errors"
)

type notificationUsecase struct {
	notificationRepo repository.NotificationRepository
	userRepo         repository.UserRepository
	emailService     EmailTemplateService
}

func NewNotificationUsecase(
	notificationRepo repository.NotificationRepository,
	userRepo repository.UserRepository,
	emailService EmailTemplateService,
) NotificationUsecase {
	return &notificationUsecase{
		notificationRepo: notificationRepo,
		userRepo:         userRepo,
		emailService:     emailService,
	}
}

func (uc *notificationUsecase) Create(ctx context.Context, userID uuid.UUID, notificationType domain.NotificationType, title, message string) (*domain.Notification, error) {
	notification, err := domain.NewNotification(userID, notificationType, title, message)
	if err != nil {
		return nil, errors.NewValidationError(err.Error())
	}

	if err := uc.notificationRepo.Create(ctx, notification); err != nil {
		return nil, errors.NewDatabaseError("failed to create notification", err)
	}

	go uc.sendNotificationAsync(ctx, notification)

	return notification, nil
}

func (uc *notificationUsecase) GetByUserID(ctx context.Context, userID uuid.UUID, unreadOnly bool, limit, offset int) ([]*domain.Notification, error) {
	notifications, err := uc.notificationRepo.GetByUserID(ctx, userID, unreadOnly, limit, offset)
	if err != nil {
		return nil, errors.NewDatabaseError("failed to get notifications", err)
	}

	return notifications, nil
}

func (uc *notificationUsecase) MarkAsRead(ctx context.Context, notificationID uuid.UUID, userID uuid.UUID) error {
	notification, err := uc.notificationRepo.GetByID(ctx, notificationID)
	if err != nil {
		return errors.NewNotFoundError("notification not found")
	}

	if notification.UserID != userID {
		return errors.NewForbiddenError("access denied")
	}

	if err := uc.notificationRepo.MarkAsRead(ctx, notificationID); err != nil {
		return errors.NewDatabaseError("failed to mark notification as read", err)
	}

	return nil
}

func (uc *notificationUsecase) SendPendingNotifications(ctx context.Context) error {
	return nil
}

func (uc *notificationUsecase) sendNotificationAsync(ctx context.Context, notification *domain.Notification) {
	user, err := uc.userRepo.GetByID(ctx, notification.UserID)
	if err != nil {
		return
	}

	if user.ShouldReceiveEmailNotifications() {
		uc.sendEmailNotification(user, notification)
	}

	notification.MarkAsSent()
	uc.notificationRepo.Update(ctx, notification)
}

func (uc *notificationUsecase) sendEmailNotification(user *domain.User, notification *domain.Notification) {
	subject := fmt.Sprintf("Smooth Parking - %s", notification.Title)
	body := uc.buildEmailBody(user, notification)
	uc.emailService.SendEmail(user.Email, subject, body)
}

func (uc *notificationUsecase) buildEmailBody(user *domain.User, notification *domain.Notification) string {
	if user.PreferredLanguage == domain.LanguageCodeJapanese {
		return fmt.Sprintf(`こんにちは %s さん,

%s

%s

よろしくお願いいたします。
Smooth Parking チーム`, user.Name, notification.Title, notification.Message)
	}

	return fmt.Sprintf(`Hello %s,

%s

%s

Best regards,
Smooth Parking Team`, user.Name, notification.Title, notification.Message)
}

func (uc *notificationUsecase) CreateParkingNotification(ctx context.Context, userID uuid.UUID, session *domain.Session) error {
	title := session.GetNotificationTitle()
	message := session.GetNotificationMessage()
	
	notification, err := domain.NewNotification(userID, domain.NotificationTypeParked, title, message)
	if err != nil {
		return err
	}
	
	notification.SetSessionID(session.ID)
	
	return uc.notificationRepo.Create(ctx, notification)
}

func (uc *notificationUsecase) CreatePaymentNotification(ctx context.Context, userID uuid.UUID, payment *domain.Payment) error {
	title := "Payment Completed"
	message := fmt.Sprintf("Your parking payment of %s has been processed successfully.", payment.GetAmountInCurrency())
	
	notification, err := domain.NewNotification(userID, domain.NotificationTypePaymentCompleted, title, message)
	if err != nil {
		return err
	}
	
	notification.SetPaymentID(payment.ID)
	
	return uc.notificationRepo.Create(ctx, notification)
}
