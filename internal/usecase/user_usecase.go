package usecase

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/repository"
)

type userUsecase struct {
	userRepo              repository.UserRepository
	verificationTokenRepo repository.VerificationTokenRepository
	emailService          EmailTemplateService
}

func NewUserUsecase(userRepo repository.UserRepository, verificationTokenRepo repository.VerificationTokenRepository, emailService EmailTemplateService) UserUsecase {
	return &userUsecase{
		userRepo:              userRepo,
		verificationTokenRepo: verificationTokenRepo,
		emailService:          emailService,
	}
}

func (uc *userUsecase) GetByID(ctx context.Context, id uuid.UUID) (*domain.User, error) {
	if id == uuid.Nil {
		return nil, fmt.Errorf("user ID is required")
	}

	user, err := uc.userRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get user by ID: %w", err)
	}

	return user, nil
}

func (uc *userUsecase) GetByEmail(ctx context.Context, email string) (*domain.User, error) {
	if email == "" {
		return nil, fmt.Errorf("email is required")
	}

	user, err := uc.userRepo.GetByEmail(ctx, email)
	if err != nil {
		return nil, fmt.Errorf("failed to get user by email: %w", err)
	}

	return user, nil
}

func (uc *userUsecase) UpdateProfile(ctx context.Context, userID uuid.UUID, name, phone *string, autoPaymentEnabled, notifyEmail, notifyPush *bool) (*domain.User, error) {
	if userID == uuid.Nil {
		return nil, fmt.Errorf("user ID is required")
	}

	user, err := uc.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	user.UpdateProfile(name, phone, autoPaymentEnabled, notifyEmail, notifyPush)

	// Save updated user
	if err := uc.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return user, nil
}

func (uc *userUsecase) UpdateExtendedProfile(ctx context.Context, userID uuid.UUID, name, phone *string, email, username *string, preferredLanguage *domain.LanguageCode, defaultPaymentMethodID *string, autoPaymentEnabled, notifyEmail, notifyPush *bool) (*domain.User, error) {
	if userID == uuid.Nil {
		return nil, fmt.Errorf("user ID is required")
	}

	user, err := uc.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Store original values for comparison
	originalEmail := user.Email
	originalUsername := user.Username
	emailChanged := false

	// Validate uniqueness for email and username if they're being changed
	if email != nil && *email != originalEmail {
		exists, err := uc.userRepo.ExistsByEmail(ctx, *email)
		if err != nil {
			return nil, fmt.Errorf("failed to check email uniqueness: %w", err)
		}
		if exists {
			return nil, fmt.Errorf("email already exists")
		}
		emailChanged = true
	}

	if username != nil && *username != originalUsername {
		exists, err := uc.userRepo.ExistsByUsername(ctx, *username)
		if err != nil {
			return nil, fmt.Errorf("failed to check username uniqueness: %w", err)
		}
		if exists {
			return nil, fmt.Errorf("username already exists")
		}
	}

	// Update user profile
	if err := user.UpdateExtendedProfile(name, phone, email, username, preferredLanguage, defaultPaymentMethodID, autoPaymentEnabled, notifyEmail, notifyPush); err != nil {
		return nil, fmt.Errorf("failed to update user profile: %w", err)
	}

	// Save updated user
	if err := uc.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	// Handle email re-verification if email was changed
	if emailChanged && email != nil {
		// Generate new verification token for the new email
		tokenData, err := domain.NewEmailVerificationToken(user.ID, *email)
		if err != nil {
			// Log error but don't fail the update
			fmt.Printf("Failed to generate email verification token: %v\n", err)
		} else {
			verificationToken := tokenData.ToStorageModel()

			// Replace any existing email verification tokens
			if err := uc.verificationTokenRepo.ReplaceVerificationToken(ctx, user.ID, domain.VerificationTypeEmailVerification, verificationToken); err != nil {
				// Log error but don't fail the update
				fmt.Printf("Failed to store email verification token: %v\n", err)
			} else {
				// Send verification email asynchronously
				go func() {
					if err := uc.emailService.SendEmailChangeVerification(user, *email, tokenData.Token); err != nil {
						fmt.Printf("Failed to send email change verification: %v\n", err)
					}
				}()
			}
		}
	}

	return user, nil
}

func (uc *userUsecase) List(ctx context.Context, limit, offset int) ([]*domain.User, error) {
	if limit <= 0 {
		limit = 10
	}
	if offset < 0 {
		offset = 0
	}

	users, err := uc.userRepo.List(ctx, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to list users: %w", err)
	}

	return users, nil
}
