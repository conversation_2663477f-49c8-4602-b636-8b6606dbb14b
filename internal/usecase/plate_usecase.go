package usecase

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/repository"
)

type plateUsecase struct {
	plateRepo repository.PlateRepository
}

func NewPlateUsecase(plateRepo repository.PlateRepository) PlateUsecase {
	return &plateUsecase{
		plateRepo: plateRepo,
	}
}

func (u *plateUsecase) Create(ctx context.Context, userID uuid.UUID, region, classification, hiragana, serialNumber string, plateType domain.PlateType) (*domain.Plate, error) {
	if userID == uuid.Nil {
		return nil, errors.New("user ID is required")
	}

	if err := u.validatePlateComponents(region, classification, hiragana, serialNumber, plateType); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	if err := u.validatePlateType(plateType); err != nil {
		return nil, fmt.Errorf("invalid plate type: %w", err)
	}

	plate, err := domain.NewPlate(userID, region, classification, hiragana, serialNumber, plateType)
	if err != nil {
		return nil, fmt.Errorf("failed to create plate domain object: %w", err)
	}

	exists, err := u.plateRepo.ExistsByPlateNumber(ctx, plate.PlateNumber)
	if err != nil {
		return nil, fmt.Errorf("failed to check plate number existence: %w", err)
	}
	if exists {
		return nil, errors.New("plate number already exists")
	}

	if err := u.plateRepo.Create(ctx, plate); err != nil {
		return nil, fmt.Errorf("failed to create plate: %w", err)
	}

	return plate, nil
}

func (u *plateUsecase) GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.Plate, error) {
	if userID == uuid.Nil {
		return nil, errors.New("user ID is required")
	}

	plates, err := u.plateRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get plates by user ID: %w", err)
	}

	return plates, nil
}

func (u *plateUsecase) GetByID(ctx context.Context, id uuid.UUID) (*domain.Plate, error) {
	if id == uuid.Nil {
		return nil, errors.New("plate ID is required")
	}

	plate, err := u.plateRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("plate not found")
		}
		return nil, fmt.Errorf("failed to get plate by ID: %w", err)
	}

	return plate, nil
}

func (u *plateUsecase) Delete(ctx context.Context, id uuid.UUID, userID uuid.UUID) error {
	if id == uuid.Nil {
		return errors.New("plate ID is required")
	}
	if userID == uuid.Nil {
		return errors.New("user ID is required")
	}

	plate, err := u.plateRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("plate not found")
		}
		return fmt.Errorf("failed to get plate: %w", err)
	}

	if plate.UserID != userID {
		return errors.New("unauthorized: plate does not belong to user")
	}

	if err := u.plateRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete plate: %w", err)
	}

	return nil
}

func (u *plateUsecase) validatePlateComponents(region, classification, hiragana, serialNumber string, plateType domain.PlateType) error {
	if strings.TrimSpace(region) == "" {
		return errors.New("region is required")
	}
	if strings.TrimSpace(classification) == "" {
		return errors.New("classification is required")
	}
	if strings.TrimSpace(hiragana) == "" {
		return errors.New("hiragana is required")
	}
	if strings.TrimSpace(serialNumber) == "" {
		return errors.New("serial number is required")
	}

	if len(region) > 10 {
		return errors.New("region must be 10 characters or less")
	}
	if len(classification) != 3 {
		return errors.New("classification must be exactly 3 digits")
	}
	if len([]rune(hiragana)) != 1 {
		return errors.New("hiragana must be exactly 1 character")
	}
	if len(serialNumber) != 4 {
		return errors.New("serial number must be exactly 4 digits")
	}

	classificationRegex := regexp.MustCompile(`^\d{3}$`)
	if !classificationRegex.MatchString(classification) {
		return errors.New("classification must contain only digits")
	}

	serialNumberRegex := regexp.MustCompile(`^\d{4}$`)
	if !serialNumberRegex.MatchString(serialNumber) {
		return errors.New("serial number must contain only digits")
	}

	if err := u.validateHiraganaForPlateType(hiragana, plateType); err != nil {
		return err
	}

	return nil
}

func (u *plateUsecase) validateHiraganaForPlateType(hiragana string, plateType domain.PlateType) error {
	switch plateType {
	case domain.PlateTypeNormal:
		validNormalHiragana := []string{
			"さ", "す", "せ", "そ", "た", "ち", "つ", "て", "と", "な", "に", "ぬ", "ね", "の",
			"は", "ひ", "ふ", "ほ", "ま", "み", "む", "め", "も", "や", "ゆ", "ら", "り", "る", "ろ",
		}
		for _, valid := range validNormalHiragana {
			if hiragana == valid {
				return nil
			}
		}
		return errors.New("invalid hiragana character for normal vehicle")

	case domain.PlateTypeCommercial:
		validCommercialHiragana := []string{"あ", "い", "う", "え", "か", "き", "く", "け", "こ", "を"}
		for _, valid := range validCommercialHiragana {
			if hiragana == valid {
				return nil
			}
		}
		return errors.New("invalid hiragana character for commercial vehicle")

	case domain.PlateTypeRental:
		validRentalHiragana := []string{"わ", "れ"}
		for _, valid := range validRentalHiragana {
			if hiragana == valid {
				return nil
			}
		}
		return errors.New("invalid hiragana character for rental vehicle")

	case domain.PlateTypeMilitary:
		validMilitaryHiragana := []string{"よ", "E", "H", "K", "M", "T", "Y"}
		for _, valid := range validMilitaryHiragana {
			if hiragana == valid {
				return nil
			}
		}
		return errors.New("invalid hiragana character for military vehicle")

	default:
		return errors.New("invalid plate type")
	}
}

func (u *plateUsecase) validatePlateType(plateType domain.PlateType) error {
	switch plateType {
	case domain.PlateTypeNormal, domain.PlateTypeCommercial, domain.PlateTypeRental, domain.PlateTypeMilitary:
		return nil
	default:
		return errors.New("invalid plate type")
	}
}
