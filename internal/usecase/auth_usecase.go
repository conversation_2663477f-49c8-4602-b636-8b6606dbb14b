package usecase

import (
	"context"
	"errors"
	"fmt"
	"net"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/repository"
)

type AuthUsecaseImpl struct {
	userRepo              repository.UserRepository
	userSessionRepo       repository.UserSessionRepository
	verificationTokenRepo repository.VerificationTokenRepository
	jwtService            JWTService
	emailService          EmailTemplateService
	authService           *domain.AuthService
}

func NewAuthUsecase(
	userRepo repository.UserRepository,
	userSessionRepo repository.UserSessionRepository,
	verificationTokenRepo repository.VerificationTokenRepository,
	jwtService JWTService,
	emailService EmailTemplateService,
) AuthUsecase {
	return &AuthUsecaseImpl{
		userRepo:              userRepo,
		userSessionRepo:       userSessionRepo,
		verificationTokenRepo: verificationTokenRepo,
		jwtService:            jwtService,
		emailService:          emailService,
		authService:           domain.NewAuthService(),
	}
}

func (a *AuthUsecaseImpl) Register(ctx context.Context, req *domain.RegisterRequest) (*domain.User, error) {
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	existsByEmail, err := a.userRepo.ExistsByEmail(ctx, req.Email)
	if err != nil {
		return nil, fmt.Errorf("failed to check email existence: %w", err)
	}
	if existsByEmail {
		return nil, errors.New("user with this email already exists")
	}

	existsByUsername, err := a.userRepo.ExistsByUsername(ctx, req.Username)
	if err != nil {
		return nil, fmt.Errorf("failed to check username existence: %w", err)
	}
	if existsByUsername {
		return nil, errors.New("user with this username already exists")
	}

	user, err := domain.NewUser(req.Username, req.Email, req.Name, req.Password, req.PreferredLanguage)
	if err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	if req.Phone != nil {
		user.Phone = req.Phone
	}

	tokenData, err := domain.NewEmailVerificationToken(user.ID, user.Email)
	if err != nil {
		return nil, fmt.Errorf("failed to generate verification token: %w", err)
	}

	verificationToken := tokenData.ToStorageModel()

	if err := a.userRepo.CreateUserWithVerificationToken(ctx, user, verificationToken); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	go func() {
		if err := a.emailService.SendEmailVerification(user, tokenData.Token); err != nil {
			// TODO: Add proper logging
			fmt.Printf("Failed to send verification email: %v\n", err)
		}
	}()

	return user, nil
}

func (a *AuthUsecaseImpl) Login(ctx context.Context, req *domain.LoginRequest, ipAddress, userAgent string) (*domain.AuthResponse, error) {
	loginData, err := a.authService.ValidateLoginRequest(req, ipAddress, userAgent)
	if err != nil {
		return nil, err
	}

	user, err := a.userRepo.GetByEmailOrUsername(ctx, loginData.Username)
	if err != nil {
		return nil, errors.New("invalid username/email or password")
	}

	if !user.CheckPassword(loginData.Password) {
		return nil, errors.New("invalid username/email or password")
	}

	if !user.CanLogin() {
		if !user.EmailVerified {
			return nil, errors.New("email not verified")
		}
		return nil, errors.New("account is not active")
	}

	sessionID := uuid.New()
	tokens, err := a.jwtService.GenerateTokenPair(user, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	session, err := a.authService.PrepareUserSession(
		user.ID,
		loginData.DeviceID,
		loginData.DeviceName,
		loginData.DeviceType,
		loginData.UserAgent,
		loginData.IP,
		tokens.RefreshToken,
		tokens.RefreshTokenExpiresAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create session: %w", err)
	}
	session.ID = sessionID

	_, err = a.userSessionRepo.CreateOrUpdateSessionWithLogin(ctx, user.ID, loginData.DeviceID, session, user)
	if err != nil {
		return nil, fmt.Errorf("failed to save session: %w", err)
	}

	return &domain.AuthResponse{
		User:   user,
		Tokens: tokens,
	}, nil
}

func (a *AuthUsecaseImpl) RefreshToken(ctx context.Context, req *domain.RefreshTokenRequest, ipAddress, userAgent string) (*domain.TokenPair, error) {
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	_, err := a.jwtService.ValidateRefreshToken(req.RefreshToken)
	if err != nil {
		return nil, errors.New("invalid refresh token")
	}

	tokenHash := a.authService.HashRefreshToken(req.RefreshToken)
	session, err := a.userSessionRepo.GetByRefreshTokenHash(ctx, tokenHash)
	if err != nil {
		return nil, errors.New("invalid refresh token")
	}

	if !session.IsValid() {
		return nil, errors.New("session expired")
	}

	user, err := a.userRepo.GetByID(ctx, session.UserID)
	if err != nil {
		return nil, errors.New("user not found")
	}

	if !user.CanLogin() {
		if !user.EmailVerified {
			return nil, errors.New("email not verified")
		}
		return nil, errors.New("account is not active")
	}

	tokens, err := a.jwtService.GenerateTokenPair(user, session.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	if err := session.UpdateRefreshToken(tokens.RefreshToken, tokens.RefreshTokenExpiresAt); err != nil {
		return nil, fmt.Errorf("failed to update refresh token: %w", err)
	}

	if ipAddress != "" || userAgent != "" {
		var ip net.IP
		if ipAddress != "" {
			ip = net.ParseIP(ipAddress)
		}
		session.UpdateDeviceInfo("", "", userAgent, ip)
	}

	session.UpdateLastUsed()
	if err := a.userSessionRepo.Update(ctx, session); err != nil {
		return nil, fmt.Errorf("failed to update session: %w", err)
	}

	return tokens, nil
}

func (a *AuthUsecaseImpl) VerifyEmail(ctx context.Context, req *domain.EmailVerificationRequest) (*domain.AuthResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	tokenHash := a.authService.HashVerificationToken(req.Token)
	verificationToken, err := a.verificationTokenRepo.GetByTokenHash(ctx, tokenHash)
	if err != nil {
		return nil, domain.NewTokenNotFoundError()
	}

	if !verificationToken.IsValid() {
		if verificationToken.IsExpired() {
			return nil, domain.NewTokenExpiredError()
		}
		return nil, domain.NewTokenUsedError()
	}

	if verificationToken.Type != domain.VerificationTypeEmailVerification {
		return nil, domain.NewTokenInvalidError()
	}

	// Generate tokens for automatic login
	sessionID := uuid.New()
	tokens, err := a.jwtService.GenerateTokenPair(&domain.User{ID: verificationToken.UserID}, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	session, err := domain.NewUserSession(
		verificationToken.UserID,
		"email-verification",
		"Email Verification",
		"web",
		"",
		nil,
		tokens.RefreshToken,
		tokens.RefreshTokenExpiresAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create session: %w", err)
	}
	session.ID = sessionID

	if err := a.userRepo.VerifyEmailWithSession(ctx, verificationToken.UserID, verificationToken, session); err != nil {
		return nil, fmt.Errorf("failed to verify email: %w", err)
	}

	user, err := a.userRepo.GetByID(ctx, verificationToken.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return &domain.AuthResponse{
		User:   user,
		Tokens: tokens,
	}, nil
}

func (a *AuthUsecaseImpl) ResendEmailVerification(ctx context.Context, req *domain.ResendVerificationRequest) error {
	if err := req.Validate(); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	user, err := a.userRepo.GetByEmail(ctx, req.Email)
	if err != nil {
		// Don't reveal if email exists or not for security
		return nil
	}

	if user.EmailVerified {
		return errors.New("email is already verified")
	}

	tokenData, err := domain.NewEmailVerificationToken(user.ID, user.Email)
	if err != nil {
		return fmt.Errorf("failed to generate verification token: %w", err)
	}

	verificationToken := tokenData.ToStorageModel()

	if err := a.verificationTokenRepo.ReplaceVerificationToken(ctx, user.ID, domain.VerificationTypeEmailVerification, verificationToken); err != nil {
		return fmt.Errorf("failed to store verification token: %w", err)
	}

	if err := a.emailService.SendEmailVerification(user, tokenData.Token); err != nil {
		return fmt.Errorf("failed to send verification email: %w", err)
	}

	return nil
}

func (a *AuthUsecaseImpl) ForgotPassword(ctx context.Context, req *domain.ForgotPasswordRequest) error {
	if err := req.Validate(); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	user, err := a.userRepo.GetByEmail(ctx, req.Email)
	if err != nil {
		// Don't reveal if email exists or not for security
		return nil
	}

	if !user.CanLogin() {
		return nil // Don't reveal account status
	}

	tokenData, err := domain.NewPasswordResetToken(user.ID, user.Email)
	if err != nil {
		return fmt.Errorf("failed to generate password reset token: %w", err)
	}

	verificationToken := tokenData.ToStorageModel()

	if err := a.verificationTokenRepo.ReplaceVerificationToken(ctx, user.ID, domain.VerificationTypePasswordReset, verificationToken); err != nil {
		return fmt.Errorf("failed to store password reset token: %w", err)
	}

	if err := a.emailService.SendPasswordReset(user, tokenData.Token); err != nil {
		return fmt.Errorf("failed to send password reset email: %w", err)
	}

	return nil
}

func (a *AuthUsecaseImpl) ResetPassword(ctx context.Context, req *domain.ResetPasswordRequest) error {
	if err := req.Validate(); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	tokenHash := a.authService.HashVerificationToken(req.Token)
	verificationToken, err := a.verificationTokenRepo.GetByTokenHash(ctx, tokenHash)
	if err != nil {
		return domain.NewTokenNotFoundError()
	}

	if !verificationToken.IsValid() {
		if verificationToken.IsExpired() {
			return domain.NewTokenExpiredError()
		}
		return domain.NewTokenUsedError()
	}

	if verificationToken.Type != domain.VerificationTypePasswordReset {
		return domain.NewTokenInvalidError()
	}

	user, err := a.userRepo.GetByID(ctx, verificationToken.UserID)
	if err != nil {
		return errors.New("user not found")
	}

	if err := user.SetPassword(req.Password); err != nil {
		return fmt.Errorf("failed to set password: %w", err)
	}

	if err := a.userRepo.ResetPasswordWithCleanup(ctx, verificationToken.UserID, user.PasswordHash, verificationToken); err != nil {
		return fmt.Errorf("failed to reset password: %w", err)
	}

	return nil
}

func (a *AuthUsecaseImpl) ChangePassword(ctx context.Context, userID uuid.UUID, req *domain.ChangePasswordRequest) error {
	if err := req.Validate(); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	user, err := a.userRepo.GetByID(ctx, userID)
	if err != nil {
		return errors.New("user not found")
	}

	if err := user.ChangePassword(req.CurrentPassword, req.NewPassword); err != nil {
		return err
	}

	if err := a.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("failed to update user: %w", err)
	}

	return nil
}

func (a *AuthUsecaseImpl) GetUserSessions(ctx context.Context, userID uuid.UUID, currentSessionID uuid.UUID) (*domain.SessionsResponse, error) {
	sessions, err := a.userSessionRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user sessions: %w", err)
	}

	sessionResponses := make([]domain.SessionResponse, 0, len(sessions))
	for _, session := range sessions {
		isCurrent := session.ID == currentSessionID
		sessionResponse := session.ToSessionResponse(isCurrent)
		sessionResponses = append(sessionResponses, *sessionResponse)
	}

	return &domain.SessionsResponse{
		Sessions: sessionResponses,
	}, nil
}

func (a *AuthUsecaseImpl) LogoutSession(ctx context.Context, userID uuid.UUID, sessionID uuid.UUID) error {
	session, err := a.userSessionRepo.GetByID(ctx, sessionID)
	if err != nil {
		return errors.New("session not found")
	}

	if session.UserID != userID {
		return errors.New("session not found")
	}

	if err := a.userSessionRepo.Delete(ctx, sessionID); err != nil {
		return fmt.Errorf("failed to delete session: %w", err)
	}

	return nil
}

func (a *AuthUsecaseImpl) LogoutAllOtherSessions(ctx context.Context, userID uuid.UUID, currentSessionID uuid.UUID) error {
	sessions, err := a.userSessionRepo.GetByUserID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user sessions: %w", err)
	}

	for _, session := range sessions {
		if session.ID != currentSessionID {
			if err := a.userSessionRepo.Delete(ctx, session.ID); err != nil {
				fmt.Printf("Failed to delete session %s: %v\n", session.ID, err)
			}
		}
	}

	return nil
}
