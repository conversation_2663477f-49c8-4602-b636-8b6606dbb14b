package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime/types"

	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type Controller struct {
	authController         *AuthController
	userController         *UserController
	parkingLotController   *ParkingLotController
	sessionController      *SessionController
	paymentController      *PaymentController
	bookingController      *BookingController
	notificationController *NotificationController
	hardwareController     *HardwareController
}

func NewController(
	authUsecase usecase.AuthUsecase,
	userUsecase usecase.UserUsecase,
	plateUsecase usecase.PlateUsecase,
	parkingLotUsecase usecase.ParkingLotUsecase,
	sessionUsecase usecase.SessionUsecase,
	paymentUsecase usecase.PaymentUsecase,
	paymentMethodUsecase usecase.PaymentMethodUsecase,
	webhookUsecase usecase.WebhookUsecase,
	bookingUsecase usecase.BookingUsecase,
	notificationUsecase usecase.NotificationUsecase,
	hardwareUsecase usecase.HardwareUsecase,
	logger *logger.Logger,
) *Controller {
	return &Controller{
		authController:         NewAuthController(authUsecase, logger),
		userController:         NewUserController(userUsecase, plateUsecase, logger),
		parkingLotController:   NewParkingLotController(parkingLotUsecase, logger),
		sessionController:      NewSessionController(sessionUsecase, logger),
		paymentController:      NewPaymentController(paymentUsecase, paymentMethodUsecase, webhookUsecase, logger),
		bookingController:      NewBookingController(bookingUsecase, logger),
		notificationController: NewNotificationController(notificationUsecase, logger),
		hardwareController:     NewHardwareController(hardwareUsecase, logger),
	}
}

func (ctrl *Controller) PostAuthLogin(c *gin.Context)    { ctrl.authController.PostAuthLogin(c) }
func (ctrl *Controller) PostAuthRefresh(c *gin.Context)  { ctrl.authController.PostAuthRefresh(c) }
func (ctrl *Controller) PostAuthRegister(c *gin.Context) { ctrl.authController.PostAuthRegister(c) }
func (ctrl *Controller) PostAuthForgotPassword(c *gin.Context) {
	ctrl.authController.PostAuthForgotPassword(c)
}
func (ctrl *Controller) PostAuthResetPassword(c *gin.Context) {
	ctrl.authController.PostAuthResetPassword(c)
}
func (ctrl *Controller) GetAuthVerifyEmail(c *gin.Context, params api.GetAuthVerifyEmailParams) {
	ctrl.authController.GetAuthVerifyEmail(c, params)
}
func (ctrl *Controller) PostAuthResendVerification(c *gin.Context) {
	ctrl.authController.PostAuthResendVerification(c)
}
func (ctrl *Controller) PostAuthChangePassword(c *gin.Context) {
	ctrl.authController.PostAuthChangePassword(c)
}

func (ctrl *Controller) GetAuthSessions(c *gin.Context) {
	ctrl.authController.GetAuthSessions(c)
}
func (ctrl *Controller) DeleteAuthSessionsSessionId(c *gin.Context, sessionId types.UUID) {
	ctrl.authController.DeleteAuthSessionsSessionId(c, sessionId)
}
func (ctrl *Controller) PostAuthSessionsLogoutAll(c *gin.Context) {
	ctrl.authController.PostAuthSessionsLogoutAll(c)
}

func (ctrl *Controller) GetBookings(c *gin.Context, params api.GetBookingsParams) {
	ctrl.bookingController.GetBookings(c, params)
}
func (ctrl *Controller) PostBookings(c *gin.Context) { ctrl.bookingController.PostBookings(c) }
func (ctrl *Controller) DeleteBookingsBookingId(c *gin.Context, bookingId types.UUID) {
	ctrl.bookingController.DeleteBookingsBookingId(c, bookingId)
}
func (ctrl *Controller) GetBookingsBookingId(c *gin.Context, bookingId types.UUID) {
	ctrl.bookingController.GetBookingsBookingId(c, bookingId)
}

func (ctrl *Controller) PostHardwareDetection(c *gin.Context) {
	ctrl.hardwareController.PostHardwareDetection(c)
}

func (ctrl *Controller) GetNotifications(c *gin.Context, params api.GetNotificationsParams) {
	ctrl.notificationController.GetNotifications(c, params)
}
func (ctrl *Controller) PatchNotificationsNotificationIdMarkRead(c *gin.Context, notificationId types.UUID) {
	ctrl.notificationController.PatchNotificationsNotificationIdMarkRead(c, notificationId)
}

func (ctrl *Controller) GetParkingLots(c *gin.Context, params api.GetParkingLotsParams) {
	ctrl.parkingLotController.GetParkingLots(c, params)
}
func (ctrl *Controller) GetParkingLotsLotId(c *gin.Context, lotId types.UUID) {
	ctrl.parkingLotController.GetParkingLotsLotId(c, lotId)
}
func (ctrl *Controller) GetParkingLotsLotIdAvailability(c *gin.Context, lotId types.UUID) {
	ctrl.parkingLotController.GetParkingLotsLotIdAvailability(c, lotId)
}

func (ctrl *Controller) GetAdminParkingLotsLotIdPricingConfigs(c *gin.Context, lotId types.UUID, params api.GetAdminParkingLotsLotIdPricingConfigsParams) {
	ctrl.parkingLotController.GetAdminParkingLotsLotIdPricingConfigs(c, lotId, params)
}
func (ctrl *Controller) PostAdminParkingLotsLotIdPricingConfigs(c *gin.Context, lotId types.UUID) {
	ctrl.parkingLotController.PostAdminParkingLotsLotIdPricingConfigs(c, lotId)
}
func (ctrl *Controller) DeleteAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	ctrl.parkingLotController.DeleteAdminParkingLotsLotIdPricingConfigsConfigId(c, lotId, configId)
}
func (ctrl *Controller) GetAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	ctrl.parkingLotController.GetAdminParkingLotsLotIdPricingConfigsConfigId(c, lotId, configId)
}
func (ctrl *Controller) PutAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	ctrl.parkingLotController.PutAdminParkingLotsLotIdPricingConfigsConfigId(c, lotId, configId)
}
func (ctrl *Controller) PostAdminParkingLotsLotIdPricingConfigsConfigIdActivate(c *gin.Context, lotId types.UUID, configId types.UUID) {
	ctrl.parkingLotController.PostAdminParkingLotsLotIdPricingConfigsConfigIdActivate(c, lotId, configId)
}
func (ctrl *Controller) PostAdminPricingConfigsCalculatePreview(c *gin.Context) {
	ctrl.parkingLotController.PostAdminPricingConfigsCalculatePreview(c)
}
func (ctrl *Controller) PostAdminPricingConfigsValidate(c *gin.Context) {
	ctrl.parkingLotController.PostAdminPricingConfigsValidate(c)
}

func (ctrl *Controller) GetPayments(c *gin.Context, params api.GetPaymentsParams) {
	ctrl.paymentController.GetPayments(c, params)
}
func (ctrl *Controller) PostPaymentsWebhooksStripe(c *gin.Context) {
	ctrl.paymentController.PostPaymentsWebhooksStripe(c)
}
func (ctrl *Controller) PostPaymentsSessionIdCreatePaymentLink(c *gin.Context, sessionId types.UUID) {
	ctrl.paymentController.PostPaymentsSessionIdCreatePaymentLink(c, sessionId)
}
func (ctrl *Controller) PostPaymentsSessionIdProcessAutoPayment(c *gin.Context, sessionId types.UUID) {
	ctrl.paymentController.PostPaymentsSessionIdProcessAutoPayment(c, sessionId)
}

func (ctrl *Controller) GetPaymentMethods(c *gin.Context) {
	ctrl.paymentController.GetPaymentMethods(c)
}
func (ctrl *Controller) PostPaymentMethods(c *gin.Context) {
	ctrl.paymentController.PostPaymentMethods(c)
}
func (ctrl *Controller) DeletePaymentMethodsPaymentMethodId(c *gin.Context, paymentMethodId string) {
	ctrl.paymentController.DeletePaymentMethodsPaymentMethodId(c, paymentMethodId)
}
func (ctrl *Controller) PostPaymentMethodsPaymentMethodIdSetDefault(c *gin.Context, paymentMethodId string) {
	ctrl.paymentController.PostPaymentMethodsPaymentMethodIdSetDefault(c, paymentMethodId)
}
func (ctrl *Controller) PostPaymentMethodsStripeCallback(c *gin.Context) {
	ctrl.paymentController.PostPaymentMethodsStripeCallback(c)
}
func (ctrl *Controller) GetPaymentMethodsValidateSetup(c *gin.Context) {
	ctrl.paymentController.GetPaymentMethodsValidateSetup(c)
}

func (ctrl *Controller) GetSessions(c *gin.Context, params api.GetSessionsParams) {
	ctrl.sessionController.GetSessions(c, params)
}
func (ctrl *Controller) GetSessionsActive(c *gin.Context) {
	ctrl.sessionController.GetSessionsActive(c)
}
func (ctrl *Controller) GetSessionsSessionId(c *gin.Context, sessionId types.UUID) {
	ctrl.sessionController.GetSessionsSessionId(c, sessionId)
}

func (ctrl *Controller) GetUsersProfile(c *gin.Context) { ctrl.userController.GetUsersProfile(c) }
func (ctrl *Controller) PutUsersProfile(c *gin.Context) { ctrl.userController.PutUsersProfile(c) }

func (ctrl *Controller) GetUsersPlates(c *gin.Context)  { ctrl.userController.GetUsersPlates(c) }
func (ctrl *Controller) PostUsersPlates(c *gin.Context) { ctrl.userController.PostUsersPlates(c) }
func (ctrl *Controller) DeleteUsersPlatesPlateId(c *gin.Context, plateId types.UUID) {
	ctrl.userController.DeleteUsersPlatesPlateId(c, plateId)
}
