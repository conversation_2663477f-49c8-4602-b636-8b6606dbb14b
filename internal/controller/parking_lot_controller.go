package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime/types"
	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/infra/http/response"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type ParkingLotController struct {
	parkingLotUsecase usecase.ParkingLotUsecase
	logger            *logger.Logger
}

func NewParkingLotController(parkingLotUsecase usecase.ParkingLotUsecase, logger *logger.Logger) *ParkingLotController {
	return &ParkingLotController{
		parkingLotUsecase: parkingLotUsecase,
		logger:            logger,
	}
}

func (plc *ParkingLotController) GetParkingLots(c *gin.Context, params api.GetParkingLotsParams) {
	plc.logger.LogInfo(c.Request.Context(), "Getting parking lots", map[string]interface{}{
		"method": "GET",
		"path":   "/parking-lots",
	})
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Get parking lots feature not implemented yet")
}

func (plc *ParkingLotController) GetParkingLotsLotId(c *gin.Context, lotId types.UUID) {
	plc.logger.LogInfo(c.Request.Context(), "Getting parking lot by ID", map[string]interface{}{
		"method": "GET",
		"path":   "/parking-lots/{lotId}",
		"lot_id": lotId,
	})
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Get parking lot by ID feature not implemented yet")
}

func (plc *ParkingLotController) GetParkingLotsLotIdAvailability(c *gin.Context, lotId types.UUID) {
	plc.logger.LogInfo(c.Request.Context(), "Getting parking lot availability", map[string]interface{}{
		"method": "GET",
		"path":   "/parking-lots/{lotId}/availability",
		"lot_id": lotId,
	})
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Get parking lot availability feature not implemented yet")
}

func (plc *ParkingLotController) GetAdminParkingLotsLotIdPricingConfigs(c *gin.Context, lotId types.UUID, params api.GetAdminParkingLotsLotIdPricingConfigsParams) {
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Get admin parking lot pricing configs feature not implemented yet")
}

func (plc *ParkingLotController) PostAdminParkingLotsLotIdPricingConfigs(c *gin.Context, lotId types.UUID) {
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Create admin parking lot pricing config feature not implemented yet")
}

func (plc *ParkingLotController) DeleteAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Delete admin parking lot pricing config feature not implemented yet")
}

func (plc *ParkingLotController) GetAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Get admin parking lot pricing config feature not implemented yet")
}

func (plc *ParkingLotController) PutAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Update admin parking lot pricing config feature not implemented yet")
}

func (plc *ParkingLotController) PostAdminParkingLotsLotIdPricingConfigsConfigIdActivate(c *gin.Context, lotId types.UUID, configId types.UUID) {
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Activate admin parking lot pricing config feature not implemented yet")
}

func (plc *ParkingLotController) PostAdminPricingConfigsCalculatePreview(c *gin.Context) {
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Calculate admin pricing configs preview feature not implemented yet")
}

func (plc *ParkingLotController) PostAdminPricingConfigsValidate(c *gin.Context) {
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Validate admin pricing configs feature not implemented yet")
}
