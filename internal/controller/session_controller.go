package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime/types"
	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type SessionController struct {
	sessionUsecase usecase.SessionUsecase
	logger         *logger.Logger
}

func NewSessionController(sessionUsecase usecase.SessionUsecase, logger *logger.Logger) *SessionController {
	return &SessionController{
		sessionUsecase: sessionUsecase,
		logger:         logger,
	}
}

func (sc *SessionController) GetSessions(c *gin.Context, params api.GetSessionsParams) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Get sessions not implemented yet"})
}

func (sc *SessionController) PostSessions(c *gin.Context) {
	c.<PERSON>(http.StatusNotImplemented, gin.H{"message": "Create session not implemented yet"})
}

func (sc *SessionController) GetSessionsActive(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Get active sessions not implemented yet"})
}

func (sc *SessionController) GetSessionsSessionId(c *gin.Context, sessionId types.UUID) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Get session by ID not implemented yet"})
}

func (sc *SessionController) PatchSessionsSessionId(c *gin.Context, sessionId types.UUID) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Update session not implemented yet"})
}
