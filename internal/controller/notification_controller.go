package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime/types"

	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type NotificationController struct {
	notificationUsecase usecase.NotificationUsecase
	logger              *logger.Logger
}

func NewNotificationController(notificationUsecase usecase.NotificationUsecase, logger *logger.Logger) *NotificationController {
	return &NotificationController{
		notificationUsecase: notificationUsecase,
		logger:              logger,
	}
}

func (nc *NotificationController) GetNotifications(c *gin.Context, params api.GetNotificationsParams) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Get notifications not implemented yet"})
}

func (nc *NotificationController) PatchNotificationsNotificationIdMarkRead(c *gin.Context, notificationId types.UUID) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Mark notification as read not implemented yet"})
}
