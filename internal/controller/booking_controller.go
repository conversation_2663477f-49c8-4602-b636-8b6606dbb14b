package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime/types"

	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type BookingController struct {
	bookingUsecase usecase.BookingUsecase
	logger         *logger.Logger
}

func NewBookingController(bookingUsecase usecase.BookingUsecase, logger *logger.Logger) *BookingController {
	return &BookingController{
		bookingUsecase: bookingUsecase,
		logger:         logger,
	}
}

func (bc *BookingController) GetBookings(c *gin.Context, params api.GetBookingsParams) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Get bookings not implemented yet"})
}

func (bc *BookingController) PostBookings(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Create booking not implemented yet"})
}

func (bc *BookingController) DeleteBookingsBookingId(c *gin.Context, bookingId types.UUID) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Delete booking not implemented yet"})
}

func (bc *BookingController) GetBookingsBookingId(c *gin.Context, bookingId types.UUID) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Get booking by ID not implemented yet"})
}
