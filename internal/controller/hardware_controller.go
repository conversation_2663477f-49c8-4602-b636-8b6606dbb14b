package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type HardwareController struct {
	hardwareUsecase usecase.HardwareUsecase
	logger          *logger.Logger
}

func NewHardwareController(hardwareUsecase usecase.HardwareUsecase, logger *logger.Logger) *HardwareController {
	return &HardwareController{
		hardwareUsecase: hardwareUsecase,
		logger:          logger,
	}
}

func (hc *HardwareController) PostHardwareDetection(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Hardware detection not implemented yet"})
}
