# API Code Generation Script
param(
    [switch]$Watch,
    [switch]$Help,
    [switch]$Bundle
)

if ($Help) {
    Write-Host "API Code Generation Script" -ForegroundColor Green
    Write-Host "Usage: .\scripts\generate.ps1 [options]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Watch    Watch for changes in OpenAPI spec and regenerate automatically"
    Write-Host "  -Bundle   Bundle multiple YAML files before generation (requires additional tools)"
    Write-Host "  -Help     Show this help message"
    exit 0
}

function Generate-API {
    Write-Host "Generating API code from OpenAPI specification..." -ForegroundColor Green
    
    if ($Bundle) {
        Write-Host "Bundling OpenAPI specifications..." -ForegroundColor Cyan

        # Check if redocly is available
        try {
            & redocly --version | Out-Null
        } catch {
            Write-Host "Error: Redocly CLI is not installed or not in PATH" -ForegroundColor Red
            Write-Host "Install it with: npm install -g @redocly/cli" -ForegroundColor Yellow
            exit 1
        }

        # For now, use the comprehensive parking-api.yaml as the base
        # and create a bundled version with enhanced metadata
        Write-Host "Creating enhanced bundled spec from parking-api.yaml..." -ForegroundColor Cyan

        # Copy the comprehensive spec to bundled location
        Copy-Item "api/spec/parking-api.yaml" "api/spec/bundled.yaml" -Force

        # Validate the bundled spec
        Write-Host "Validating bundled specification..." -ForegroundColor Cyan
        & redocly lint api/spec/bundled.yaml
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Warning: Bundled spec has validation issues, but continuing..." -ForegroundColor Yellow
        }

        Write-Host "Bundle created successfully: api/spec/bundled.yaml" -ForegroundColor Green
        $specFile = "api/spec/bundled.yaml"
    } else {
        $specFile = "api/spec/parking-api.yaml"
    }
    
    try {
        Write-Host "Generating server code..." -ForegroundColor Cyan
        & oapi-codegen -config api/config/server.yaml $specFile
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Server code generation failed!" -ForegroundColor Red
            exit 1
        }

        Write-Host "Generating models code..." -ForegroundColor Cyan
        & oapi-codegen -config api/config/models.yaml $specFile
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Models code generation failed!" -ForegroundColor Red
            exit 1
        }

        Write-Host "Generating types code..." -ForegroundColor Cyan
        & oapi-codegen -config api/config/types.yaml $specFile
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Types code generation failed!" -ForegroundColor Red
            exit 1
        }
        
        Write-Host "Code generation completed successfully!" -ForegroundColor Green
    } catch {
        Write-Host "Error: $_" -ForegroundColor Red
        exit 1
    }
}

if ($Watch) {
    Write-Host "Watching for changes in api/spec/*.yaml..." -ForegroundColor Yellow
    Write-Host "Press Ctrl+C to stop watching" -ForegroundColor Yellow
    
    # Initial generation
    Generate-API
    
    # Watch for file changes
    $watcher = New-Object System.IO.FileSystemWatcher
    $watcher.Path = "api/spec"
    $watcher.Filter = "*.yaml"
    $watcher.IncludeSubdirectories = $true
    $watcher.NotifyFilter = [System.IO.NotifyFilters]::LastWrite
    $watcher.EnableRaisingEvents = $true
    
    $action = {
        Write-Host "OpenAPI spec changed, regenerating..." -ForegroundColor Yellow
        Generate-API
    }
    
    Register-ObjectEvent -InputObject $watcher -EventName "Changed" -Action $action
    
    try {
        while ($true) {
            Start-Sleep 1
        }
    } finally {
        $watcher.Dispose()
    }
} else {
    Generate-API
} 