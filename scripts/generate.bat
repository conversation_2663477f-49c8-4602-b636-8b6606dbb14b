@echo off
setlocal

REM Check for bundle flag
set "BUNDLE_MODE=false"
if "%1"=="--bundle" set "BUNDLE_MODE=true"
if "%1"=="-b" set "BUNDLE_MODE=true"

echo Generating API code from OpenAPI specification...

REM Set spec file based on bundle mode
if "%BUNDLE_MODE%"=="true" (
    echo Bundling OpenAPI specifications...

    REM Check if redocly is available
    redocly --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo Error: Redocly CLI is not installed or not in PATH
        echo Install it with: npm install -g @redocly/cli
        exit /b 1
    )

    REM For now, use the comprehensive parking-api.yaml as the base
    echo Creating enhanced bundled spec from parking-api.yaml...
    copy "api\spec\parking-api.yaml" "api\spec\bundled.yaml" >nul
    if %errorlevel% neq 0 (
        echo Failed to create bundled spec!
        exit /b 1
    )

    REM Validate the bundled spec
    echo Validating bundled specification...
    redocly lint api/spec/bundled.yaml
    if %errorlevel% neq 0 (
        echo Warning: Bundled spec has validation issues, but continuing...
    )

    echo Bundle created successfully: api/spec/bundled.yaml
    set "SPEC_FILE=api/spec/bundled.yaml"
) else (
    set "SPEC_FILE=api/spec/parking-api.yaml"
)

echo Generating server code...
oapi-codegen -config api/config/server.yaml %SPEC_FILE%
if %errorlevel% neq 0 (
    echo Server code generation failed!
    exit /b 1
)

echo Generating models code...
oapi-codegen -config api/config/models.yaml %SPEC_FILE%
if %errorlevel% neq 0 (
    echo Models code generation failed!
    exit /b 1
)

echo Generating types code...
oapi-codegen -config api/config/types.yaml %SPEC_FILE%
if %errorlevel% neq 0 (
    echo Types code generation failed!
    exit /b 1
)

echo Code generation completed successfully!