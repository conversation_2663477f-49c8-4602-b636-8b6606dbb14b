@echo off
echo Generating API code from OpenAPI specification...

echo Generating server code...
oapi-codegen -config api/config/server.yaml api/spec/parking-api.yaml
if %errorlevel% neq 0 (
    echo Server code generation failed!
    exit /b 1
)

echo Generating models code...
oapi-codegen -config api/config/models.yaml api/spec/parking-api.yaml
if %errorlevel% neq 0 (
    echo Models code generation failed!
    exit /b 1
)

echo Generating types code...
oapi-codegen -config api/config/types.yaml api/spec/parking-api.yaml
if %errorlevel% neq 0 (
    echo Types code generation failed!
    exit /b 1
)

echo Code generation completed successfully!