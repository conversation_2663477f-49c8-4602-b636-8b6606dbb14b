// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package api

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
)

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+x9W28cyXnoXyn0WeBQODOaoSQba76cQ1GSJS8lE6S0ezYyTRS7v5mpVU93b1U1qbFM",
	"IEMYhhPHMBIjD0FgBAGCIMgVCBDkIUDyYwb+H0Hduqu7q2+c4Yi7zhs5XdfvVl99t/rg+fE8iSOIOPP2",
	"PngUWBJHDOQ/j3FwDF+nwLj4z48jDpH8EydJSHzMSRyNvmJxJH5j/gzmWPz1CYWJt+f9r1E+9Eh9ZaOn",
	"lMb0WE/iXV1dDbwAmE9JIgbz9sScyEx6NfAO4mgSEn+LC8hmvBp4z2J6ToIAou1Nn095NfBeRBxohMMT",
	"oBdAZd/trcRMjtTsSE1/NfBexfxZnEbB9pbyKuZITXk18N5EOOWzmJKfwBaXUJhVfNY9xcD7PicXmMMR",
	"JT6JpoKCyNTinITGCVBOFFfBZAKiAzyj8Vz8MInpHHNvzwswhyEnc/AGHl8k4O15jFMSTcW2s25vIk7C",
	"rv2uBh6Fr1NCBazeluY+zZrH51+BIvn9lM8yOFTWjn0fGHsdv1MsUV3k+4RQYC/sryTiMAVJOQlezCHi",
	"J8DT5Dhb1ocSqL+YAZ8BRbo1YqI5IgxlO8nWfR7HIWDJKxQmFNisfm1ymBcZrTRRw0neNKeJgZcyoG1d",
	"34g2AuwuyMZHaku1tIHncaqWVxJKOPTTEHMIUILpOxJN0QQAkQj94OjLHBwWqP2sxzOAtkU/A3hMAb8L",
	"4ksJyylEQDGHY/CBJHo9E5yG3NvjNIUyc3xft0c4ChCDKEBU9UQ45fEcc+LjMFw48Saav4o5mWjGbZ3s",
	"RIxvaENsJgTxBUX2IK6ZUgZP1MAaDS+Bz+KgdcI3DJBA/f9mSLfLpp+rEaqzlfhO47WMltM2Mqnlw4xO",
	"XJinwWOKlXyucIH4eogZf+T+mlIKkb8owMQr0FjemEQXMfHhVTo/V3xRaTEHxvBUrh/eY4Epb8/Tm0MJ",
	"jYUwgQCxVIqVSVqgkXyYBJNgn3cXlRo7L4JClzQlQUNrRQyv5VfHVjRBv6GhW7hwzFPWxmd66yeqsexG",
	"SQL6ZyVwXrjxpmFkfbNprUJFj+NYSIkq5fg48iEMJZdoyeAiIdkIekHdpyDouk8XiILX4lPnDrM4peHi",
	"GPOahZOuKJcy9DDm7RjLWhb6daWtUC+1cQ7ZyLTuODLjmPJ+wOtGo5pychrlMcfhfoPASZOgL+KFOO20",
	"0QbaPsm2A1E6F1LWF4oXnUsFISNh8bc6JOTfUXzGZvGlJXrzVR3McDSFI8zYZUyD2kNaCUlu2jlOa9UA",
	"JaaFY64ILusHeAWXWWe0MycRmadz9CnyZ5hinwNl97yBNyfRIURTPvP2Pm1T/MprLi7AdQ4dSHbWsK5X",
	"Zvty8M1Y6LaYogQlM1dpmfbAudCqB5pk6HryCTFjBXVnjt8bRD50CT1C8RRHuNR0tw5Y5gxrlTmyoQTB",
	"tLqS3bELwEAJDvMT32r/qA24epZBGQDWBksTNEC4001LyoPpK6zoobA31+YSNehxGkL7SW63rTBbPm9p",
	"VNeOXDppvebnVKmeONVSpAVzB/3Kmnoz+scT4OKiGUdPCFV/2IIaIk7FSuA94U5JnHVvuoo6Rz3Taoge",
	"/YyCH9NA/s+AMRJHZxos6jDQw7gWUblC9biToZ1JTJFYAVIzSIldPTstdNaJyicptS5G9gpei5M5mzzQ",
	"7cQK5iRKObCuq9AE0Eb0rquJETq5THBfDQMkyA/JtihSjZ0CRuKoq7zvR5RP55iEnwPNZE+t5ODGilDc",
	"jBwAXVgjINkSTWg8R3M8JT4KSfSu9aBR47ukQdEQ5RBpgZtYAuCYhDYo8jHBGA6bLmiVb+LEZBzPkx6H",
	"aWXqgmWhsptzzKD2EhJgEi4OcLKfJCGBoKYRYb7QTJv00wmJcFg7TUSmM942TXwBlJKgbrE1OzcSgsTR",
	"EYULApe1BHfzs2fgMR8iTEksOxIOc9bxVnOiO3r5+jGleFHVi+wZ7QlPu2+8jqL7r94s+xhYGnLn4quL",
	"iuk05q2qPQgGL9C7+qXVripbnXaYuNe5brohCgw4IhHjNFWCHDFx0PMYLeKUovpVVhb0HNPgElOwjtga",
	"YDw8rErAh8OATAlHRR0uF+gJ5hyoaPnjt+Ph904/PLz6xCW5D3YdByrhC3SBwxTQzg9wgiNggJTWiELy",
	"DtDvfrP83b//Vl59WvTU547hd1EIYnHouVY3UUwRpvPFkII6xvX3naeD54PPBi8Hrwdflicr7nC1/PPV",
	"8i9Wy79cLX+7Wv7VavnXq+XfrJZ/u1r+3Wr596vlP6yW/7ha/tNq+c+r5b+slv+6Wv7bavkfq+V/rpb/",
	"tbr+w9X1cnV9vbr+2er656vrP1pd//Hq+per61+tlter5c9Wy5+vlr9YLX+5Wv5qtfz1avmnq+VvVtd/",
	"trr+9er6T1bXv3j6/LOXr788dcL3xZMqAB5p3CkduwFnj9w4k2ptAJHvUIYyakJ5K8T8mIpzIj884vQ8",
	"BAVScaWVANXXW28vR6Re2tXAI3M8BW10K874Q/kHDpFsgt4cH6pTODArabh9Esed+7saOkadCmOOxD44",
	"mRA3mL7rBlNuZilOMN5D8hoJgWBbMQ3akSrrvQHa3UMhTDjiMygsYEfobvfkpVPquOPB7qlLgeOO+V6b",
	"8xvFkwJUbnAx5kLsH+x6AyEUJHtJErMBmm3cJQUP4ympFzQWmCu7eMOARngOglmllEM4CKhQ+pzorbOo",
	"iGEa7DGl7Zbw3mAlOYx51Q6FpYtNkrnAUYQFy4h7b8wgcF41Xirp3+l4KG7MdEGmRbOaXHJ0dNGaX2kF",
	"yWVLlj86GNgiTVCOiPxQG3+6Nx4X+Wnn7Xi4e/p/JVv99MHb8fDh6b29t+Phd07lT5/U2nqKIz94sIGR",
	"S6SgplHbGMgdu8ig7MAqwam/ZbyjIZuwEyiovZa7q8PVsocVrpdLRegmfXbb79bHCQ9rriwdLF82qowB",
	"bNsW7MoaLOEhUAPKBClBfmZbr8U9hHG8OLvENBLDK/sSnOEQJJmeK3vtGYU5iYKCDS1f+1HB/VEyq2jp",
	"6vQOCgFmzKtrsq8fRxz7/GgWR25c3oBptJF7/wKTEJ+TkPBFd/9OoZe5fr7E742zqSh2XyoFBslWSPq+",
	"G5zxAWEcu/Um/UUabYAD1TYbBpj6M6H3pyGXVpuqElVRliaAeUqheJOqYRJzWRp4EwrwUtmLqut7RiHX",
	"SLRVybnFGYhj4pDMCT+Yuy/RRedd6RhrgWBXeSh0wZ77J+zBo5lbhIorLE+DkjuhFv5hHE37tI+0Zbqy",
	"xDiBaEN8Jngb85i+qpurm1cw13OMR/AkiTnbkEPQJSNrOLMqsNTXEBoWFGLG32irb2dpEvt+muDIzyi2",
	"A0abIdO8TeXMaPNibEROqi6PF514aqvxaj00n32lZTsZt79+s4b9bcPkzupvAaEQrzUkrmmuj/1PRzWU",
	"BWI8mTComUYSeE/azsyMDu8xp4t+/mNxIe7Xo0bKlvT8CGvnrlmRNdWpc2+Zy6RbCJ+yUDeH7K0RuHVT",
	"baljrNcEkzClcAxYh9XemG9bg8ZuGO21XvwWBU4XB/U+BH1DaQ8cVc36e7JuPX7MbnlIoneN7XKbRvXi",
	"s+W7kl5yi1+s3sUlvxxot5m5ZJGIpZMJ8Ym4YE3SKGCe4rCzAPyQRNp/nAjpcCZ+V85icyXLwj51DKO4",
	"cakVnDp5JwzPsf9OWSwdSva+UWGQaYpi1VbZvec4SqWjV3n3tYexu4bbZAmQhC/mdh+kLtPRBIeso+3I",
	"orebBbPqgPKbRH82cUOSr8stERo2k0cNl5yZRnSXwwRogOQ3tPM5YXiAXmLGgQqqGiDg/v17m4qmFLBa",
	"vIwjPqtZhmyhPDZz0Qzt7A53H7hjAtRoXwKm7YMtRKv6a5tDeXtiiKku+J/PCEOESYt456hrpek/qo4q",
	"Dk70CEkLP0PxBAnoN4Uh2NKyMS7GmJyK8x0VA3Fko0Eep6gESoIXCV44RcZHErIy7+FzHJIAtwTf5JEg",
	"TyMhPRrSOETboUEdYQhUBzSJqcSuzKpw4XKGWX28vptm1GhohhnCNWSDGITg85oskhlmN5osWpSmYQgH",
	"Qc0khElAH2jDXucNXQjMlLcjwGiDmDmnjOA9P+GQuGJN33NEwY/nc4gCCBDjkCAem9QKUPk3FvniIKie",
	"hAz4mQZ49aNC+ZlYpvnoGSI/qxyzETh5Qmvr8t7nioaqAC0CCJjYCcfvQAdAiX/VagpA83odZ1WPTwJR",
	"YMyxRiMoBR4LDVr+QUEoHDWOoCMTMN4WM1oSyC53PNrZffCw5MB+uJmjxg5JLa4kc65nscpoZ7W8rvrR",
	"b+ni3xSL9iwNQzTNAtLaYtHWDaYtTl6OaNjpHs1Qjrot50NZ7nyJ80elQR/dhRPGBqZhm0iMEypGmQP1",
	"ifyHQsTlH3MSEo6p+4g8osSHH+q4LGdg+tomAuNcSYCepZGyvDQ7OzOnZEeIRoSfzXPbezb47sBlYHHY",
	"K4ruycJ4ldU7zRi5LasKwwAvijYlg7eXMo7gdSpW8IUUaq9nqTfwnlHiDbwTLFZ0krqjasuXE42oHmbu",
	"n/74waO98fiTOjvejRC2ppl9DUxKKG8Uk8yFShIuzjo57ElEOMHh2YQCOLfk7BXG/KxG25cRlmLu7gbK",
	"LObAZZ7UPN/D3FkQFY4hqQFa5+EMz7RFTmqw1EB1YOHFrMKF22Mrt7k2iiaAC+LrTJmaUCnVxAprQsrX",
	"KC1ViFPsv1OKi0P5asyurqR6WK3dG5oScQlePxAzF96Os14LSesofODO/KiNG8oiMOcp4+gcEOYoBHGb",
	"tLOx2pKxBl5i/Nyl4cXPuQpSWKhTusEEKIXgEEfTNIsHMrbbr7BXzl0+Mj1QqLsgPw4A7XyFJe6NVjJA",
	"EMkfnkbTkLCZFXOmhgW3NJd3g37p1JmeHuvM/tKFJkjF4FJNYpw2pnNTN+7fROTrVF0CZOiY2FcYT0lU",
	"BPF3xgW0PSyG+OHhT/aHfzAefu9sePp/2uOEstUMMkq1ws0ikwtUxp+bORhEQac8hYxNXHkKOpRDAJrK",
	"IcuZC3ZUwbpxzmLR7fHVSaeUx3Z2qsnPkIvQKRkmPlpn7nfbnxq3JeavtNV1IrrzzCy30A1k9pQ2Ueaj",
	"jAw1d1C8S9HyVVOOyns4rhyBrZrbOQml1doKHqnqBed29kefGhSTuoQNK17lDatL2KjV6KUDs2HFThDm",
	"zp+N+fr637mzEN6DQhx2h6iELkkyJnusEZnSO/rCCsl2ZPT39+hSGtOXDS4KeE9486S9fcKdbQ1HuKDU",
	"2uGV0iljX0CbwhBuu85AtwQ+47S+7coEHVyZmqdyV+a2DRJ6AQ2pdjaLVlSYSNoZjep8iRnKE0+78nOd",
	"vv6krKbXd3/lVICep3McDSngQFo6tdqvlZCakV47HRl6KaIP2pnH5ySEAQqAveNxMkBcDM/vrcNhyX4e",
	"cFqqP3aUaTDxxIa2m1N10YVuHiUdKVodseRKEgeMiwCkP0kgVo1qk0HKOtNAA1k2JBXYRmW7ykW9A1qP",
	"2RBYpDfQIwevxD2dkvBcZbUcpm4iPVE+BQfgT6RjzpQEk0Mh1QMx1cUZ6cJ4PDeywjmgaYJkVkv1hElm",
	"MAeKw89g4VC3zVf0DhbytqE4BZ08+cydp5Kei0uW4B7ngHpRVjMxcgcVsXQPOj6UqzGN0CSMLxGZSM8I",
	"1ETuZyhqgFYB/C6IObGvIkuk50uHOdRnBBUdxMXAg7L5myPM+viIHeUO3OAv3kvdlLE5gBWyTgqjVpfs",
	"upaoANe7VxmjZqFvWIP5p4t/WX0YBYRl7jxZaS7DW8EVVyWDwOFb7kMNmee1MKuT3ztd03cuSRiaGoeI",
	"wrBwWycT5MvyRMG9Dtf2jVjFZEG9xdOatRehr1Lz7BJ8NX5oOeZRymatQyYpm3UY8VYta9s0pX18e1aV",
	"U3XBy27M2WDzexq18iiKoyzRRNaFcSH7Rtfm7bN5O3tCXooEai6YHTVooabK9No+QGmXDm3SoI21b8qo",
	"G+TMjnxH4xC6VHU9jpW/p9v9VvQox+keFPTQTbnkO8mM7vx+rMFhQKfDs3AwJ274WTt1XVdYyhIZWCTU",
	"GB0m4xqmS9iZvOL0tFLKoCk3Reokyl4DVsEmlUA/pYQvTgTyde10wBTofqpiMc/lf88MTn/wxWtPV5CW",
	"S5JfcxTNOE/sUh1mFCIQOwMcyMYK797/H5pmw9faem1WnBBxu5DFrEk0iU2xbKzKqWtp5bE0SWLK/x+b",
	"xzGf3SeRnw9+In9DLyLfq1TEPpljmpdLmOMIT0HVa14wDnN0SfjMEpwh8SFioKNuMmPm4EdR9SwYyELC",
	"FHAoKd8xyf0fiY3qMcVu9IL3E+zPAD24P/YGXiouRRKabG80ury8vI/l5/sxnY50XzY6fHHw9NXJ0+GD",
	"++P7Mz4PrRRjs39tnUMv803uH73wBt4FUKari9wf3x+b3D2cEG/Pe3h/fP+hSkefSZoYSSYa6d0Mw5iz",
	"0Ycw5i+Cq5FWsYdKM5fNp+oCrDL4dCqB933g+2IUK19ImgkLej9TlRmwyij19t5q2hEryZEb6uKG+cVD",
	"Hd15xfRWs5oe9+sUZD03PTCJ/DAN4EWUC4FsyNYo8tNB8RGCB+NxryrvPROgdNZdlc+vqq5UCWFV4GSq",
	"zeUMUeCUwEW5zN7VwHs0fli3jmyHo0K9/0fjR+09svL7UvKk8zmmC0UYhdIhiXO5AolYkNdbT5IRGmbE",
	"fWA39E7F0RszBwUexewOkeCp6gyMP46DxcbeA2goM3lVvKyLBV9VaHZ3YyupkmpH0jQGaQddjtupzHr7",
	"4wakXKBMBUsUwaWbKHvQ5NWgjxAdfVB/vAiulOgx0dZFgn4if+9A0gd6tC1L1+K4fr6INbmmQLCPNkaw",
	"5Vo2nelV4edjy1FFDWtT6mCd4/vbTGjjOyAZ79KZzRLwyYT4G6C4JHUd1+nvIcVtXiFosK53UgjuAtk7",
	"60FvRyFYj08U8D+e9jDC+oUnaZJYRys2fGaejPoffqurNd30ptY3huMM3XwDec4gYJNcV2KwrMD6MFFV",
	"gruwV5GdzAi6zLB3O8TYWMt5y8TYXF7Z9aIgAPLzHkjD2kqE+9gXRL0JWT7fXqm0IBryo7r49Ppkd6Es",
	"zdCb2j43HW+Hykqe861SlcP63iDkJC4QpqBSki2a+WiLucj6IJ1puy5VGmyvR4Epn42U435oB7830F3K",
	"Z8WHiG6J3NyvHW2Z7roYC0zIvI5/2Iy42m3vUn3yMzdpyaXkbzRpH/EMIi7gAEFW1cDQSv6xRBwTWSC+",
	"B3EUK8rf1onnrJe/7aPOXTu/iUZUWoWKRpEZIGsTi+Xe8/bentpUoNvkZCBn74R1Fb7RiutDHeVxGygu",
	"1OTeMmYLr8w68CnXZmfH3Jxna3CnZujMpTqjsR1jOlXzlnDmSgS9Y6iTa0MaXk5pvZ7o1SBA6iliZDK2",
	"uqBQ5Zx2waFueVtILCa/btmh1IZAWSrfAGtTx+332rvkD683ily1LoSlP6kH/zKIgkJIZRc6KCdi3hpF",
	"1GV83j11zF7l9g5amb4K1ee3zGtbnQigj5ZVSPK8RbR/bB3LnczarmKVz+ZtqeANFGIpYqmMKO1+Ntj5",
	"L7V+u5TPTCaNd4sYqWTrOJBh2jR6stY7Z78P3OROqzAexPLNd4eo0HTjlA9xGLYzndnWoeyzH4bex5V1",
	"+2GIYpk8ZraDONA5iWrMZutBXO1avWKEwzCfE977kHCTtNYP/B+yOrRdYiAsHJxk9Wu7uCiY1fpueos7",
	"oPvEFELZKI7XM8PbRJG5iq20xVZKkMflYpjFpjeJN3m26xjvCtZv/BSmK0TQiOZ6WtkmbbSpxPZmP6oB",
	"qub0U3grvo+lz8AcE92OQ/2ATONJ+Ni0cUuGEqp1vPygIy5K78/XxZimiR/PVWrXXQsu1TvoElNqIFl7",
	"jtcdyec5Cgw6M6w0h25ayLu9wMnSs/FbvuFmCKgF+GZjI3tfbsuhkeoyq3HqRqnNm6MP+q9Oh7oZ5LHp",
	"0+lAP7da380DvQuWTXr8Zm6nOdrkuAg3o2zQKkB/D1Gi36luvLisF1B3Xpypnp1mOm9mlD+O2XhDqbyc",
	"e0sitPaF3i2bBqqP8Dvwmj/8qtN3NsVsuarzoZQI9fb0qmjKd2cWKU3UIFm/tToyD6oamjBDa5oo5hs3",
	"6ECvSonJHRShNKKAg34KS432ox4Cco70wFGeczt6T+ERzA7KTwGEvTWgSma4RmgRMQ6sjj7Y/74IrkZz",
	"TN/Jajm6EKw/cwgA8XNh7FeFUV5i+u5YobddkhcX8E2+MttAQHP5dCXCApl47ZuvgGgBy9nAzci2Yzub",
	"ONgK1Gy77B7qdwB15r2ab3iOpbCTLzXWXHNDXOTS1mJtguHLrlD9qGD/yUu3oxtMfqKfocQBSVn+RmXN",
	"hKqZWyztjscuwVQj31hMa8Rb/pZmnt1t/SQLJTvyi6tbe0ZCDhSdL1D2bKZ7V9Znh0BsTRe+kQDP30fv",
	"AzX9cJtz1Ns4FbrFzLJmr0KWsdjxCNAkaaU62tLfjCe5uioPTKx3R7lwGPOtpURsBxktONiCep5UZ+uP",
	"vxEuvQnaFZmFt0S/TYgtPl/scGVY3xGJ1KKbcqEqiMvT8XERiI24k6n9Q/0mTjOmrAItazvYOmaB23Wr",
	"u6SAl5746auzMizalh4KKkBQfTFAaLbkbRZizS7JauE+p//Ersq3KQuby2DmKqsmYzCDQFCgsqdVS8HV",
	"gtlBqyNVs2Xo61p1zbaBIi7U8kyVu1syEzTU09t6fkyBj9r4RqMtq2O5YRJ5jqMgLJGInktmxedY6UcP",
	"Jm1gqN7D6izKTBz5iX5G67bRUPeIWwNeFJDyEPq6SHgyyZ8jq7yqxlGauF8j6wXmD6Vahx0s3EV4lyt9",
	"dTnlk0qfdRyT282lL+KBwjzetNp2LMdE2Hl+rY/gEQM+DPJ6n12lbAnRJ8BN0dDto3yTVtdqvbpmA6zz",
	"lcP6XN+b08EJVKaxKq/W18nrQh5ddMOOdta+xtE7cpvuo7L2UVZnhPGYLnorq0mxfxWRZQyOLuF8Fsfv",
	"jOrUiZfZF7qTOq/X0JXKZdnuWuCs3mitn6QhvkPrMhq+CKIgiUkhEqwOJVb410hp5OZkHsronE4oykLB",
	"dFWk/PHkb0lsmOuZ6gaOktE027jf2C9+F57q2ik+CX6vJyVoEhwWnkDtRwpHaoj9vBbstmnhFmoO5Jv5",
	"eMk1+Qoa4lLr6uhuKqLlwaZZq/h8fwNvmeTdfi5gTY2OAsOCa6ybZiVwssQuXQLBrSDwzYe/ld5F2ZQD",
	"+Juk45jnljrpOMrkyNri4euVnOIAFmVkaC5Sxghnzw+3Eci+KX95p2CmVlXZeW/QYfc4HSBYiklvA+O3",
	"LRI9w1UrPW/RHVOasR6LAvtsJGNtGqXkG9HuSDXbys3IvFXVRv+FcKH+ZF+oY1y43qofmm32ZajcWuVS",
	"McXHqliqMNECeYSD4OMH4O4HgfYWFPDqQmuZ+Ecf9KtoHSyUFtqP9FNqnWxUWdtvbKnPIs5v1TrZB4M0",
	"npAQ2uWXbneLp4F8Y6MuDVuv8xblf2pNY8FM7r6xqGQFPrdVc9F+KGjL17E63Ohtb66goqvaYQtmKjck",
	"+8UBfT8CeuEOaDuicZCqKFnVqFItHyfkfv4iwOhi13NEh3E8VbqDcwimPg+7DPUELiCME+2Nqgy3NxqF",
	"sY/DWcz43qfjT8difXKk0wwyH1wMhAs5TvJZAZNTZRL6zcsBxWyo6hLleNYzBxlHsHwQhRxHEF9BCuaD",
	"5D21kKp2taNz7LgNsZUs/M8MYkdg1A9ldD3XOjJtr9rdXMGlw32qKxwaW6QFhCyk2rWCyrM/Yh+5iTnb",
	"iL6V12+CgqCTMgKyEP8a/JWjhnW3Yhxpta8q9ZXtVVoVWovtO+HbXDTs6vTqvwMAAP//RAZxIZLBAAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
