openapi: 3.0.3
info:
  title: Smooth Parking Management API
  description: |
    Smart parking management system with automatic license plate detection,
    payment processing, and real-time parking management.
  version: 1.0.0
  contact:
    name: Smooth Inc
    email: <EMAIL>
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html

servers:
  - url: https://api.smooth.inc/v1
    description: Production server
  - url: https://staging-api.smooth.inc/v1
    description: Staging server
  - url: http://localhost:8080/api/v1
    description: Development server

security:
  - BearerAuth: []

paths:
  # Authentication endpoints
  /auth/register:
    post:
      tags: [Authentication]
      summary: Register a new user
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          $ref: '#/components/responses/Conflict'

  /auth/login:
    post:
      tags: [Authentication]
      summary: Login user
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/forgot-password:
    post:
      tags: [Authentication]
      summary: Request password reset
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ForgotPasswordRequest'
      responses:
        '200':
          description: Password reset email sent successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ForgotPasswordResponse'
        '400':
          $ref: '#/components/responses/BadRequest'

  /auth/reset-password:
    post:
      tags: [Authentication]
      summary: Reset password using token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPasswordRequest'
      responses:
        '200':
          description: Password reset successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResetPasswordResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/refresh:
    post:
      tags: [Authentication]
      summary: Refresh access token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenRequest'
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/verify-email:
    get:
      tags: [Authentication]
      summary: Verify email address using magic link token
      security: []
      parameters:
        - name: token
          in: query
          required: true
          schema:
            type: string
          description: Email verification token from magic link
      responses:
        '200':
          description: Email verified successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/resend-verification:
    post:
      tags: [Authentication]
      summary: Resend email verification link
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResendVerificationRequest'
      responses:
        '200':
          description: Verification email sent successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'
        '400':
          $ref: '#/components/responses/BadRequest'

  /auth/change-password:
    post:
      tags: [Authentication]
      summary: Change password for authenticated user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
      responses:
        '200':
          description: Password changed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # User management
  /users/profile:
    get:
      tags: [Users]
      summary: Get user profile
      responses:
        '200':
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      tags: [Users]
      summary: Update user profile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'

  # Session management
  /auth/sessions:
    get:
      tags: [Authentication]
      summary: Get user's active sessions
      responses:
        '200':
          description: Sessions retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionsResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/sessions/{sessionId}:
    delete:
      tags: [Authentication]
      summary: Logout from specific session
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Session terminated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/sessions/logout-all:
    post:
      tags: [Authentication]
      summary: Logout from all sessions except current
      responses:
        '200':
          description: All other sessions terminated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # License plates management
  /users/plates:
    get:
      tags: [Plates]
      summary: Get user's license plates
      responses:
        '200':
          description: License plates retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Plate'

    post:
      tags: [Plates]
      summary: Add a new license plate
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePlateRequest'
      responses:
        '201':
          description: License plate added successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Plate'
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          $ref: '#/components/responses/Conflict'

  /users/plates/{plateId}:
    delete:
      tags: [Plates]
      summary: Remove a license plate
      parameters:
        - name: plateId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: License plate removed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'
        '404':
          $ref: '#/components/responses/NotFound'

  # Parking lots
  /parking-lots:
    get:
      tags: [Parking Lots]
      summary: Search parking lots
      parameters:
        - name: lat
          in: query
          schema:
            type: number
            format: double
          description: Latitude for location-based search
        - name: lng
          in: query
          schema:
            type: number
            format: double
          description: Longitude for location-based search
        - name: radius
          in: query
          schema:
            type: integer
            default: 1000
          description: Search radius in meters
        - name: sort
          in: query
          schema:
            type: string
            enum: [distance, price]
            default: distance
        - name: features
          in: query
          schema:
            type: array
            items:
              type: string
          description: Filter by features
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
            maximum: 100
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
      responses:
        '200':
          description: Parking lots retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParkingLotsResponse'

  /parking-lots/{lotId}:
    get:
      tags: [Parking Lots]
      summary: Get parking lot details
      parameters:
        - name: lotId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Parking lot details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParkingLot'
        '404':
          $ref: '#/components/responses/NotFound'

  /parking-lots/{lotId}/availability:
    get:
      tags: [Parking Lots]
      summary: Get real-time availability
      parameters:
        - name: lotId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Availability information retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParkingLotAvailability'

  # Parking sessions
  /sessions:
    get:
      tags: [Sessions]
      summary: Get user's parking sessions
      parameters:
        - name: status
          in: query
          schema:
            $ref: '#/components/schemas/SessionStatus'
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
      responses:
        '200':
          description: Parking sessions retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Session'

  /sessions/{sessionId}:
    get:
      tags: [Sessions]
      summary: Get parking session details
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Parking session details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Session'
        '404':
          $ref: '#/components/responses/NotFound'



  /sessions/active:
    get:
      tags: [Sessions]
      summary: Get user's active parking sessions
      responses:
        '200':
          description: Active parking sessions retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Session'

  # Hardware detection endpoints
  /hardware/detection:
    post:
      tags: [Hardware]
      summary: License plate detection from hardware (entry/exit)
      security:
        - HardwareAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HardwareDetectionRequest'
      responses:
        '200':
          description: Detection processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetectionResponse'
        '400':
          $ref: '#/components/responses/BadRequest'

  # Payment endpoints
  /payments:
    get:
      tags: [Payments]
      summary: Get user's payment history
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
      responses:
        '200':
          description: Payment history retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Payment'

  # Payment methods endpoints
  /payment-methods:
    get:
      tags: [Payment Methods]
      summary: Get user's saved payment methods
      responses:
        '200':
          description: Payment methods retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PaymentMethod'

    post:
      tags: [Payment Methods]
      summary: Create Stripe setup intent for adding a new payment method
      responses:
        '200':
          description: Setup intent created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SetupIntentResponse'
        '400':
          $ref: '#/components/responses/BadRequest'

  /payment-methods/{paymentMethodId}:
    delete:
      tags: [Payment Methods]
      summary: Remove a saved payment method
      parameters:
        - name: paymentMethodId
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Payment method removed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'
        '404':
          $ref: '#/components/responses/NotFound'

  /payment-methods/{paymentMethodId}/set-default:
    post:
      tags: [Payment Methods]
      summary: Set payment method as default for automatic payments
      parameters:
        - name: paymentMethodId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Default payment method updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DefaultPaymentMethodResponse'
        '404':
          $ref: '#/components/responses/NotFound'

  /payment-methods/stripe-callback:
    post:
      tags: [Payment Methods]
      summary: Handle Stripe setup completion callback
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StripeSetupCallbackRequest'
      responses:
        '200':
          description: Payment method setup completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentMethod'
        '400':
          $ref: '#/components/responses/BadRequest'

  /payment-methods/validate-setup:
    get:
      tags: [Payment Methods]
      summary: Validate if user has payment method set up for auto-payments
      responses:
        '200':
          description: Payment setup validation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentSetupValidationResponse'

  /payments/{sessionId}/create-payment-link:
    post:
      tags: [Payments]
      summary: Create Stripe payment link for session (manual payment)
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Payment link created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentLinkResponse'
        '400':
          $ref: '#/components/responses/BadRequest'

  /payments/{sessionId}/process-auto-payment:
    post:
      tags: [Payments]
      summary: Process automatic payment for completed session
      security:
        - HardwareAuth: []
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AutoPaymentRequest'
      responses:
        '200':
          description: Automatic payment processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutoPaymentResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '402':
          description: Payment failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentErrorResponse'

  /payments/webhooks/stripe:
    post:
      tags: [Payments]
      summary: Stripe webhook endpoint
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
      responses:
        '200':
          description: Webhook processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'

  # Booking endpoints
  /bookings:
    get:
      tags: [Bookings]
      summary: Get user's bookings
      parameters:
        - name: status
          in: query
          schema:
            $ref: '#/components/schemas/BookingStatus'
        - name: upcoming
          in: query
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: Bookings retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Booking'

    post:
      tags: [Bookings]
      summary: Create a new booking
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBookingRequest'
      responses:
        '201':
          description: Booking created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          $ref: '#/components/responses/Conflict'

  /bookings/{bookingId}:
    get:
      tags: [Bookings]
      summary: Get booking details
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Booking details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
        '404':
          $ref: '#/components/responses/NotFound'

    delete:
      tags: [Bookings]
      summary: Cancel a booking
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Booking cancelled successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
        '400':
          $ref: '#/components/responses/BadRequest'

  # Notifications
  /notifications:
    get:
      tags: [Notifications]
      summary: Get user's notifications
      parameters:
        - name: unread
          in: query
          schema:
            type: boolean
            default: false
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: Notifications retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Notification'

  /notifications/{notificationId}/mark-read:
    patch:
      tags: [Notifications]
      summary: Mark notification as read
      parameters:
        - name: notificationId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Notification marked as read
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'
        '404':
          $ref: '#/components/responses/NotFound'

  # Admin endpoints for parking lot configuration
  /admin/parking-lots/{lotId}/pricing-configs:
    get:
      tags: [Admin - Parking Configuration]
      summary: Get parking lot pricing configurations
      parameters:
        - name: lotId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: includeInactive
          in: query
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: Pricing configurations retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ParkingLotConfig'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

    post:
      tags: [Admin - Parking Configuration]
      summary: Create new pricing configuration
      parameters:
        - name: lotId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePricingConfigRequest'
      responses:
        '201':
          description: Pricing configuration created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParkingLotConfig'
        '400':
          $ref: '#/components/responses/BadRequest'
        '403':
          $ref: '#/components/responses/Forbidden'

  /admin/parking-lots/{lotId}/pricing-configs/{configId}:
    get:
      tags: [Admin - Parking Configuration]
      summary: Get specific pricing configuration
      parameters:
        - name: lotId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: configId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Pricing configuration retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParkingLotConfig'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      tags: [Admin - Parking Configuration]
      summary: Update pricing configuration
      parameters:
        - name: lotId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: configId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePricingConfigRequest'
      responses:
        '200':
          description: Pricing configuration updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParkingLotConfig'
        '400':
          $ref: '#/components/responses/BadRequest'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

    delete:
      tags: [Admin - Parking Configuration]
      summary: Delete pricing configuration
      parameters:
        - name: lotId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: configId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Pricing configuration deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  /admin/parking-lots/{lotId}/pricing-configs/{configId}/activate:
    post:
      tags: [Admin - Parking Configuration]
      summary: Activate pricing configuration
      parameters:
        - name: lotId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: configId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ActivatePricingConfigRequest'
      responses:
        '200':
          description: Pricing configuration activated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParkingLotConfig'
        '400':
          $ref: '#/components/responses/BadRequest'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  /admin/pricing-configs/validate:
    post:
      tags: [Admin - Parking Configuration]
      summary: Validate pricing rules
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PricingRules'
      responses:
        '200':
          description: Pricing rules are valid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationResponse'
        '400':
          description: Pricing rules validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationResponse'
        '403':
          $ref: '#/components/responses/Forbidden'

  /admin/pricing-configs/calculate-preview:
    post:
      tags: [Admin - Parking Configuration]
      summary: Preview fee calculation with pricing rules
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FeeCalculationPreviewRequest'
      responses:
        '200':
          description: Fee calculation preview generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeeCalculationPreviewResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '403':
          $ref: '#/components/responses/Forbidden'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    HardwareAuth:
      type: apiKey
      in: header
      name: X-Hardware-Token

  schemas:
    # User and Authentication schemas
    RegisterRequest:
      type: object
      required: [username, email, password, name, preferredLanguage]
      properties:
        username:
          type: string
          minLength: 3
          maxLength: 50
          pattern: "^[a-zA-Z0-9_-]+$"
          description: "Unique username for login"
        email:
          type: string
          format: email
        password:
          type: string
          minLength: 8
          description: "Password must be at least 8 characters"
        name:
          type: string
          maxLength: 200
          description: "Full name"
        phone:
          type: string
          maxLength: 20
          description: "Phone number"
        preferredLanguage:
          type: string
          enum: [ja, en]
          default: ja
          description: "Preferred language code (ja for Japanese, en for English)"
        setupPaymentMethod:
          type: boolean
          default: true
          description: "Whether to setup payment method during registration"

    LoginRequest:
      type: object
      required: [identifier, password]
      properties:
        identifier:
          type: string
          description: "Username or email address"
        password:
          type: string
          description: "User password"

    ForgotPasswordRequest:
      type: object
      required: [email]
      properties:
        email:
          type: string
          format: email

    ForgotPasswordResponse:
      type: object
      properties:
        message:
          type: string
          example: "Password reset instructions sent to your email"

    ResetPasswordRequest:
      type: object
      required: [token, password]
      properties:
        token:
          type: string
          description: Reset token sent to user's email
        password:
          type: string
          minLength: 8
          description: New password

    ResetPasswordResponse:
      type: object
      properties:
        message:
          type: string
          example: "Password reset successful"
        redirectUrl:
          type: string
          example: "/login"

    RefreshTokenRequest:
      type: object
      required: [refreshToken]
      properties:
        refreshToken:
          type: string
        deviceId:
          type: string
          description: "Optional device identifier for session tracking"

    EmailVerificationRequest:
      type: object
      required: [token]
      properties:
        token:
          type: string
          description: "Email verification token from magic link"

    ResendVerificationRequest:
      type: object
      required: [email]
      properties:
        email:
          type: string
          format: email
          description: "Email address to resend verification to"

    ChangePasswordRequest:
      type: object
      required: [currentPassword, newPassword]
      properties:
        currentPassword:
          type: string
          description: "Current password"
        newPassword:
          type: string
          minLength: 8
          description: "New password (minimum 8 characters)"

    MessageResponse:
      type: object
      properties:
        message:
          type: string
          description: "Response message"
        success:
          type: boolean
          default: true

    SessionResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        deviceId:
          type: string
          description: "Device identifier"
        deviceName:
          type: string
          description: "Human-readable device name"
        deviceType:
          type: string
          description: "Device type (mobile, desktop, tablet)"
        ipAddress:
          type: string
          description: "IP address of the session"
        lastUsedAt:
          type: string
          format: date-time
          description: "Last time this session was used"
        createdAt:
          type: string
          format: date-time
          description: "When the session was created"
        isCurrent:
          type: boolean
          description: "Whether this is the current session"

    SessionsResponse:
      type: object
      properties:
        sessions:
          type: array
          items:
            $ref: '#/components/schemas/SessionResponse'

    AuthResponse:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/User'
        accessToken:
          type: string
        refreshToken:
          type: string
        expiresIn:
          type: integer
        paymentSetupRequired:
          type: boolean
          description: "Whether payment setup is required"
        setupIntent:
          $ref: '#/components/schemas/SetupIntentResponse'
          description: "Setup intent for payment method (if paymentSetupRequired=true)"

    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        username:
          type: string
          description: "Unique username"
        email:
          type: string
          format: email
        name:
          type: string
          description: "Full name"
        phone:
          type: string
          description: "Phone number"
        preferredLanguage:
          type: string
          enum: [ja, en]
          description: "Preferred language code"
        role:
          $ref: '#/components/schemas/UserRole'
        status:
          $ref: '#/components/schemas/UserStatus'
        stripeCustomerId:
          type: string
        defaultPaymentMethodId:
          type: string
          description: "Stripe payment method ID for automatic payments"
        autoPaymentEnabled:
          type: boolean
          default: true
          description: "Enable automatic payment processing on parking exit"
        notifyEmail:
          type: boolean
        notifyPush:
          type: boolean
        emailVerified:
          type: boolean
        lastLoginAt:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    UpdateUserRequest:
      type: object
      properties:
        name:
          type: string
          maxLength: 200
          description: "Full name"
        phone:
          type: string
          maxLength: 20
          description: "Phone number"
        email:
          type: string
          format: email
          description: "Email address (will require re-verification if changed)"
        username:
          type: string
          minLength: 3
          maxLength: 50
          pattern: "^[a-zA-Z0-9_-]+$"
          description: "Unique username for login"
        preferredLanguage:
          type: string
          enum: [ja, en]
          description: "Preferred language code (ja for Japanese, en for English)"
        defaultPaymentMethodId:
          type: string
          description: "Stripe payment method ID for automatic payments"
        autoPaymentEnabled:
          type: boolean
          description: "Enable/disable automatic payment processing"
        notifyEmail:
          type: boolean
          description: "Enable/disable email notifications"
        notifyPush:
          type: boolean
          description: "Enable/disable push notifications"

    UserRole:
      type: string
      enum: [user, admin]

    UserStatus:
      type: string
      enum: [active, suspended, pending]

    # License plate schemas
    Plate:
      type: object
      properties:
        id:
          type: string
          format: uuid
        userId:
          type: string
          format: uuid
        region:
          type: string
          maxLength: 10
          description: "Japanese region (品川)"
        classification:
          type: string
          maxLength: 3
          description: "Classification number (123)"
        hiragana:
          type: string
          maxLength: 1
          description: "Hiragana character (あ)"
        serialNumber:
          type: string
          maxLength: 4
          description: "Serial number (1234)"
        plateNumber:
          type: string
          description: "Full generated plate number"
        plateType:
          $ref: '#/components/schemas/PlateType'
        isActive:
          type: boolean
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    CreatePlateRequest:
      type: object
      required: [region, classification, hiragana, serialNumber]
      properties:
        region:
          type: string
          maxLength: 10
        classification:
          type: string
          maxLength: 3
        hiragana:
          type: string
          maxLength: 1
        serialNumber:
          type: string
          maxLength: 4
        plateType:
          $ref: '#/components/schemas/PlateType'

    PlateType:
      type: string
      enum: [normal, commercial, rental, military]

    # Parking lot schemas
    ParkingLot:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        address:
          type: string
        latitude:
          type: number
          format: double
        longitude:
          type: number
          format: double
        totalSpots:
          type: integer
        heightLimitCm:
          type: integer
        hourlyRate:
          type: integer
          description: "Rate in JPY"
        dailyMaxRate:
          type: integer
          description: "Maximum daily rate in JPY"
        freeMinutes:
          type: integer
          description: "Free parking minutes"
        is24h:
          type: boolean
        openTime:
          type: string
          pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
        closeTime:
          type: string
          pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
        features:
          type: array
          items:
            type: string
        images:
          type: array
          items:
            type: string
        status:
          $ref: '#/components/schemas/LotStatus'
        operatorName:
          type: string
        contactPhone:
          type: string
        distance:
          type: number
          format: double
          description: "Distance in meters (for search results)"
        currentAvailability:
          $ref: '#/components/schemas/ParkingLotAvailability'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    ParkingLotsResponse:
      type: object
      properties:
        lots:
          type: array
          items:
            $ref: '#/components/schemas/ParkingLot'
        total:
          type: integer
        limit:
          type: integer
        offset:
          type: integer

    ParkingLotAvailability:
      type: object
      properties:
        availableSpots:
          type: integer
        totalSpots:
          type: integer
        occupancyRate:
          type: number
          format: double
        lastUpdated:
          type: string
          format: date-time

    LotStatus:
      type: string
      enum: [active, maintenance, closed]

    # Session schemas
    Session:
      type: object
      properties:
        id:
          type: string
          format: uuid
        userId:
          type: string
          format: uuid
        plateId:
          type: string
          format: uuid
        parkingLotId:
          type: string
          format: uuid
        entryTime:
          type: string
          format: date-time
        exitTime:
          type: string
          format: date-time
        durationMinutes:
          type: integer
        amount:
          type: integer
          description: "Amount in JPY"
        discountAmount:
          type: integer
        status:
          $ref: '#/components/schemas/SessionStatus'
        isPaid:
          type: boolean
        entryImageUrl:
          type: string
        exitImageUrl:
          type: string
        detectionConfidence:
          type: number
          format: double
        errorMessage:
          type: string
        manualOverride:
          type: boolean
        parkingLot:
          $ref: '#/components/schemas/ParkingLot'
        plate:
          $ref: '#/components/schemas/Plate'
        payment:
          $ref: '#/components/schemas/Payment'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time



    SessionStatus:
      type: string
      enum: [active, completed, cancelled, error]

    # Hardware detection schemas
    HardwareDetectionRequest:
      type: object
      required: [ts, C1, "3L", H1, ID, parkingid, status]
      properties:
        ts:
          type: string
          format: date-time
          description: "Timestamp of detection"
        C1:
          type: string
          maxLength: 10
          description: "City value (Japanese region like 品川)"
        "3L":
          type: string
          pattern: "^[0-9]{3}$"
          description: "3-digit classification number"
        H1:
          type: string
          maxLength: 1
          pattern: "^[さすせそたちつてとなにぬねのはひふほまみむめもやゆらりるろあいうえかきくけこをわれよEHKMTY]$"
          description: "1 letter Hiragana or army-related letter (E,H,K,M,T,Y)"
        ID:
          type: string
          pattern: "^[0-9]{4}$"
          description: "4-digit serial number"
        parkingid:
          type: string
          pattern: "^[0-9]{6}$"
          description: "6-digit parking lot identifier"
        status:
          type: integer
          enum: [0, 1]
          description: "0: started to park (entry), 1: left the parking lot (exit)"
        imageUrl:
          type: string
          description: "Optional image URL from detection"
        confidence:
          type: number
          format: double
          minimum: 0
          maximum: 1
          description: "Detection confidence score"

    DetectionResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        sessionId:
          type: string
          format: uuid
        action:
          type: string
          enum: [entry_created, exit_recorded, session_updated, no_action]
        plateNumber:
          type: string
          description: "Generated full plate number"
        payment:
          $ref: '#/components/schemas/AutoPaymentResponse'
          description: "Payment details when exit is processed and auto-payment is triggered"
        calculatedFee:
          type: integer
          description: "Calculated parking fee in JPY (for exit actions)"
        parkingDuration:
          type: integer
          description: "Total parking duration in minutes (for exit actions)"

    DetectionDirection:
      type: string
      enum: [entry, exit]

    # Payment schemas
    Payment:
      type: object
      properties:
        id:
          type: string
          format: uuid
        sessionId:
          type: string
          format: uuid
        userId:
          type: string
          format: uuid
        amount:
          type: integer
          description: "Amount in JPY"
        currency:
          type: string
          default: "JPY"
        stripePaymentIntentId:
          type: string
        stripePaymentLinkId:
          type: string
        stripeStatus:
          type: string
        paymentMethodType:
          type: string
        cardLast4:
          type: string
        cardBrand:
          type: string
        status:
          $ref: '#/components/schemas/PaymentStatus'
        paidAt:
          type: string
          format: date-time
        receiptUrl:
          type: string
        invoiceNumber:
          type: string
        failureReason:
          type: string
        retryCount:
          type: integer
        session:
          $ref: '#/components/schemas/Session'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    PaymentLinkResponse:
      type: object
      properties:
        paymentLinkUrl:
          type: string
        paymentIntentId:
          type: string
        amount:
          type: integer
        expiresAt:
          type: string
          format: date-time

    PaymentStatus:
      type: string
      enum: [pending, processing, completed, failed, refunded]

    AutoPaymentRequest:
      type: object
      required: [amount, calculatedFee]
      properties:
        amount:
          type: integer
          description: "Calculated parking fee in JPY"
        calculatedFee:
          $ref: '#/components/schemas/FeeBreakdown'
        useDefaultPaymentMethod:
          type: boolean
          default: true
          description: "Use user's default payment method"
        generateReceipt:
          type: boolean
          default: true
          description: "Generate and send receipt automatically"
        sendNotification:
          type: boolean
          default: true
          description: "Send payment completion notification"

    AutoPaymentResponse:
      type: object
      properties:
        success:
          type: boolean
        paymentId:
          type: string
          format: uuid
        stripePaymentIntentId:
          type: string
        amount:
          type: integer
        currency:
          type: string
          default: "JPY"
        status:
          $ref: '#/components/schemas/PaymentStatus'
        receiptUrl:
          type: string
        invoiceNumber:
          type: string
        paidAt:
          type: string
          format: date-time
        paymentMethodType:
          type: string
        cardLast4:
          type: string
        cardBrand:
          type: string
        message:
          type: string
          example: "Payment processed successfully"

    PaymentErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          default: false
        error:
          type: string
        errorCode:
          type: string
          enum: [insufficient_funds, card_declined, expired_card, no_payment_method, processing_error]
        message:
          type: string
        retryable:
          type: boolean
        fallbackOptions:
          type: array
          items:
            type: string
          description: "Available fallback options like manual payment link"

    # Payment method schemas
    PaymentMethod:
      type: object
      properties:
        id:
          type: string
        userId:
          type: string
          format: uuid
        stripePaymentMethodId:
          type: string
        type:
          type: string
          enum: [card, paypay]
          description: "Payment method type"
        brand:
          type: string
          description: "Card brand (Visa, Mastercard, etc.)"
        last4:
          type: string
          description: "Last 4 digits of card number"
        expiryMonth:
          type: integer
          description: "Card expiration month (1-12)"
        expiryYear:
          type: integer
          description: "Card expiration year"
        isDefault:
          type: boolean
          description: "Whether this is the default payment method"
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    SetupIntentResponse:
      type: object
      properties:
        clientSecret:
          type: string
          description: "Stripe setup intent client secret"
        setupIntentId:
          type: string
          description: "Stripe setup intent ID"
        ephemeralKey:
          type: string
          description: "Ephemeral key for mobile SDKs"
        customerId:
          type: string
          description: "Stripe customer ID"
        publishableKey:
          type: string
          description: "Stripe publishable key"
        redirectUrl:
          type: string
          description: "URL for redirect flow if needed"

    StripeSetupCallbackRequest:
      type: object
      required: [setupIntentId, paymentMethodId]
      properties:
        setupIntentId:
          type: string
          description: "Stripe setup intent ID"
        paymentMethodId:
          type: string
          description: "Stripe payment method ID"
        isDefault:
          type: boolean
          default: false
          description: "Set as default payment method"

    DefaultPaymentMethodResponse:
      type: object
      properties:
        success:
          type: boolean
        paymentMethodId:
          type: string
        message:
          type: string
          example: "Default payment method updated successfully"

    PaymentSetupValidationResponse:
      type: object
      properties:
        isSetupComplete:
          type: boolean
          description: "Whether the user has a valid payment method for auto-payments"
        hasPaymentMethod:
          type: boolean
          description: "Whether the user has any payment methods added"
        hasDefaultPaymentMethod:
          type: boolean
          description: "Whether the user has a default payment method selected"
        autoPaymentEnabled:
          type: boolean
          description: "Whether auto-payment is enabled for the user"
        requiresAction:
          type: boolean
          description: "Whether the user needs to take action to enable auto-payment"
        nextStep:
          type: string
          enum: [add_payment_method, set_default_payment_method, enable_auto_payment, update_expired_card, none]
          description: "Next recommended step to complete setup"

    # Booking schemas
    Booking:
      type: object
      properties:
        id:
          type: string
          format: uuid
        userId:
          type: string
          format: uuid
        plateId:
          type: string
          format: uuid
        parkingLotId:
          type: string
          format: uuid
        startTime:
          type: string
          format: date-time
        endTime:
          type: string
          format: date-time
        hourlyRate:
          type: integer
        totalAmount:
          type: integer
        status:
          $ref: '#/components/schemas/BookingStatus'
        cancelledAt:
          type: string
          format: date-time
        cancellationFee:
          type: integer
        parkingLot:
          $ref: '#/components/schemas/ParkingLot'
        plate:
          $ref: '#/components/schemas/Plate'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    CreateBookingRequest:
      type: object
      required: [plateId, parkingLotId, startTime, endTime]
      properties:
        plateId:
          type: string
          format: uuid
        parkingLotId:
          type: string
          format: uuid
        startTime:
          type: string
          format: date-time
        endTime:
          type: string
          format: date-time

    BookingStatus:
      type: string
      enum: [confirmed, cancelled, completed, no_show]

    # Notification schemas
    Notification:
      type: object
      properties:
        id:
          type: string
          format: uuid
        userId:
          type: string
          format: uuid
        type:
          $ref: '#/components/schemas/NotificationType'
        title:
          type: string
        message:
          type: string
        sessionId:
          type: string
          format: uuid
        paymentId:
          type: string
          format: uuid
        parkingLotId:
          type: string
          format: uuid
        isSent:
          type: boolean
        sentAt:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    NotificationType:
      type: string
      enum: [parked, payment_completed, overstay_warning, price_alert, booking_reminder]

    # Admin - Parking Configuration schemas
    ParkingLotConfig:
      type: object
      properties:
        id:
          type: string
          format: uuid
        parkingLotId:
          type: string
          format: uuid
        configName:
          type: string
        isActive:
          type: boolean
        pricingRules:
          $ref: '#/components/schemas/PricingRules'
        effectiveFrom:
          type: string
          format: date-time
        effectiveUntil:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        createdBy:
          type: string
          format: uuid

    PricingRules:
      type: object
      required: [lot_id, initial_free_minutes, daily_cap, rules]
      properties:
        lot_id:
          type: string
        initial_free_minutes:
          type: integer
          minimum: 0
        daily_cap:
          type: integer
          minimum: 0
        night_caps:
          type: array
          items:
            $ref: '#/components/schemas/NightCap'
        overrides:
          type: array
          items:
            $ref: '#/components/schemas/PriceOverride'
        rules:
          type: array
          items:
            $ref: '#/components/schemas/PricingRule'

    NightCap:
      type: object
      required: [start, end, cap]
      properties:
        start:
          type: string
          pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
          example: "22:00"
        end:
          type: string
          pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
          example: "08:00"
        cap:
          type: integer
          minimum: 0

    PriceOverride:
      type: object
      required: [name, start, end, unit_minutes, price_per_unit]
      properties:
        name:
          type: string
        start:
          type: string
          format: date-time
        end:
          type: string
          format: date-time
        unit_minutes:
          type: integer
          minimum: 1
        price_per_unit:
          type: integer
          minimum: 0

    PricingRule:
      type: object
      required: [days, start, end, unit_minutes, price_per_unit]
      properties:
        days:
          type: array
          items:
            type: string
            enum: [Mon, Tue, Wed, Thu, Fri, Sat, Sun]
        start:
          type: string
          pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
        end:
          type: string
          pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$|^24:00$"
        unit_minutes:
          type: integer
          minimum: 1
        price_per_unit:
          type: integer
          minimum: 0

    CreatePricingConfigRequest:
      type: object
      required: [configName, pricingRules]
      properties:
        configName:
          type: string
          maxLength: 100
        pricingRules:
          $ref: '#/components/schemas/PricingRules'

    UpdatePricingConfigRequest:
      type: object
      properties:
        configName:
          type: string
          maxLength: 100
        pricingRules:
          $ref: '#/components/schemas/PricingRules'

    ActivatePricingConfigRequest:
      type: object
      required: [effectiveFrom]
      properties:
        effectiveFrom:
          type: string
          format: date-time
        effectiveUntil:
          type: string
          format: date-time

    ValidationResponse:
      type: object
      properties:
        valid:
          type: boolean
        errors:
          type: array
          items:
            type: string
        warnings:
          type: array
          items:
            type: string

    FeeCalculationPreviewRequest:
      type: object
      required: [pricingRules, scenarios]
      properties:
        pricingRules:
          $ref: '#/components/schemas/PricingRules'
        scenarios:
          type: array
          items:
            $ref: '#/components/schemas/ParkingScenario'

    ParkingScenario:
      type: object
      required: [name, entryTime, exitTime]
      properties:
        name:
          type: string
        entryTime:
          type: string
          format: date-time
        exitTime:
          type: string
          format: date-time

    FeeCalculationPreviewResponse:
      type: object
      properties:
        scenarios:
          type: array
          items:
            $ref: '#/components/schemas/ScenarioResult'

    ScenarioResult:
      type: object
      properties:
        name:
          type: string
        fee:
          type: integer
        breakdown:
          $ref: '#/components/schemas/FeeBreakdown'
        appliedRules:
          type: array
          items:
            type: string
        freeMinutesUsed:
          type: integer
        totalMinutes:
          type: integer
        billableMinutes:
          type: integer

    FeeBreakdown:
      type: object
      properties:
        baseFee:
          type: integer
        nightCapApplied:
          type: integer
        dailyCapApplied:
          type: integer
        overrideFee:
          type: integer
        discountAmount:
          type: integer
        finalFee:
          type: integer

    # Error response schemas
    ErrorResponse:
      type: object
      properties:
        error:
          type: string
        message:
          type: string
        code:
          type: string
        details:
          type: object
        timestamp:
          type: string
          format: date-time

  responses:
    BadRequest:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    NotFound:
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    Conflict:
      description: Conflict
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    InternalServerError:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

tags:
  - name: Authentication
    description: User authentication and authorization
  - name: Users
    description: User management operations
  - name: Plates
    description: License plate management
  - name: Parking Lots
    description: Parking lot information and search
  - name: Sessions
    description: Parking session management
  - name: Hardware
    description: Hardware integration endpoints
  - name: Payments
    description: Payment processing and history
  - name: Bookings
    description: Parking reservations
  - name: Notifications
    description: User notifications
  - name: Admin - Parking Configuration
    description: Admin endpoints for parking lot pricing configuration management