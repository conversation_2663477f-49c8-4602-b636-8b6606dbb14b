# API Code Generation

This directory contains OpenAPI specifications and code generation scripts for the Smooth Parking Backend API.

## Directory Structure

```
api/
├── config/           # oapi-codegen configuration files
│   ├── models.yaml   # Configuration for generating models
│   ├── server.yaml   # Configuration for generating server code
│   └── types.yaml    # Configuration for generating types
├── generated/        # Generated Go code (auto-generated)
│   ├── models.gen.go
│   ├── server.gen.go
│   └── types.gen.go
├── spec/            # OpenAPI specifications
│   ├── components/  # Modular component definitions (future use)
│   ├── paths/       # Modular path definitions (future use)
│   ├── master.yaml  # Modular master spec (future use)
│   ├── parking-api.yaml  # Comprehensive single-file spec
│   └── bundled.yaml # Generated bundled spec (created during build)
└── README.md        # This file
```

## Code Generation Scripts

### PowerShell Script (Recommended)

```powershell
# Basic generation using parking-api.yaml
.\scripts\generate.ps1

# Generate with bundling (creates comprehensive spec)
.\scripts\generate.ps1 -Bundle

# Watch mode - regenerate on file changes
.\scripts\generate.ps1 -Watch

# Show help
.\scripts\generate.ps1 -Help
```

### Batch <PERSON>ript

```batch
# Basic generation
scripts\generate.bat

# Generate with bundling
scripts\generate.bat --bundle
```

## Bundling Process

The bundling feature creates a comprehensive OpenAPI specification file with full information:

1. **Input**: Uses `parking-api.yaml` as the comprehensive source
2. **Validation**: Validates the spec using Redocly CLI
3. **Output**: Creates `api/spec/bundled.yaml` with enhanced metadata
4. **Code Generation**: Uses the bundled spec for generating Go code

### Benefits of Bundling

- **Comprehensive Spec**: Single file with all API information
- **Validation**: Ensures spec quality with Redocly linting
- **Consistency**: Standardized output format
- **Future-Ready**: Prepared for modular spec architecture

## Requirements

- **oapi-codegen**: For generating Go code from OpenAPI specs
- **Redocly CLI**: For bundling and validation (install with `npm install -g @redocly/cli`)

## Generated Files

The scripts generate three main Go files:

1. **`models.gen.go`**: Data structures and models
2. **`server.gen.go`**: HTTP server interfaces and handlers
3. **`types.gen.go`**: Type definitions and utilities

## Configuration

Each generation type has its own configuration file in `api/config/`:

- **`models.yaml`**: Controls model generation
- **`server.yaml`**: Controls server code generation  
- **`types.yaml`**: Controls type generation

## Usage in Development

1. **Modify the API spec**: Edit `api/spec/parking-api.yaml`
2. **Generate code**: Run `.\scripts\generate.ps1 -Bundle`
3. **Use generated code**: Import from `api/generated/` in your Go code

## Future Modular Architecture

The `master.yaml`, `paths/`, and `components/` directories are prepared for a future modular architecture where:

- API paths are split into separate files
- Components are organized by category
- The master file references all modules
- Bundling combines everything into a single comprehensive spec

This approach will improve maintainability for large API specifications.

## Validation Warnings

The current spec may show validation warnings for:
- Missing `operationId` fields
- Missing 4XX error responses
- Unused components

These are non-blocking warnings that don't affect code generation but can be addressed to improve API quality.
