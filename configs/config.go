package configs

import (
	"os"
	"strconv"
	"time"
)

type Config struct {
	Environment string         `json:"environment"`
	Server      ServerConfig   `json:"server"`
	Database    DatabaseConfig `json:"database"`
	JWT         JWTConfig      `json:"jwt"`
	Stripe      StripeConfig   `json:"stripe"`
	Email       EmailConfig    `json:"email"`
	AWS         AWSConfig      `json:"aws"`
}

type ServerConfig struct {
	Port           int `json:"port"`
	ReadTimeout    int `json:"read_timeout"`
	WriteTimeout   int `json:"write_timeout"`
	IdleTimeout    int `json:"idle_timeout"`
	MaxHeaderBytes int `json:"max_header_bytes"`
}

type DatabaseConfig struct {
	Host            string `json:"host"`
	Port            int    `json:"port"`
	User            string `json:"user"`
	Password        string `json:"password"`
	DBName          string `json:"db_name"`
	SSLMode         string `json:"ssl_mode"`
	TimeZone        string `json:"time_zone"`
	MaxIdleConns    int    `json:"max_idle_conns"`
	MaxOpenConns    int    `json:"max_open_conns"`
	ConnMaxLifetime int    `json:"conn_max_lifetime"` // in minutes
}

type JWTConfig struct {
	SecretKey       string        `json:"secret_key"`
	ExpiryDuration  time.Duration `json:"expiry_duration"`
	RefreshDuration time.Duration `json:"refresh_duration"`
	Issuer          string        `json:"issuer"`
}

type StripeConfig struct {
	SecretKey      string `json:"secret_key"`
	PublishableKey string `json:"publishable_key"`
	WebhookSecret  string `json:"webhook_secret"`
}

type EmailConfig struct {
	Provider string `json:"provider"`
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	From     string `json:"from"`
	FromName string `json:"from_name"`
	TLS      bool   `json:"tls"`
}

type AWSConfig struct {
	Region          string `json:"region"`
	AccessKeyID     string `json:"access_key_id"`
	SecretAccessKey string `json:"secret_access_key"`
	S3Bucket        string `json:"s3_bucket"`
	S3Region        string `json:"s3_region"`
}

func Load() *Config {
	return &Config{
		Environment: getEnv("ENVIRONMENT", "development"),
		Server: ServerConfig{
			Port:           getEnvInt("PORT", 8080),
			ReadTimeout:    getEnvInt("READ_TIMEOUT", 30),
			WriteTimeout:   getEnvInt("WRITE_TIMEOUT", 30),
			IdleTimeout:    getEnvInt("IDLE_TIMEOUT", 120),
			MaxHeaderBytes: getEnvInt("MAX_HEADER_BYTES", 1<<20), // 1 MB
		},
		Database: DatabaseConfig{
			Host:            getEnv("DB_HOST", "localhost"),
			Port:            getEnvInt("DB_PORT", 5432),
			User:            getEnv("DB_USER", "postgres"),
			Password:        getEnv("DB_PASSWORD", "root"),
			DBName:          getEnv("DB_NAME", "smooth_parking"),
			SSLMode:         getEnv("DB_SSL_MODE", "disable"),
			TimeZone:        getEnv("DB_TIMEZONE", "Asia/Tokyo"),
			MaxIdleConns:    getEnvInt("DB_MAX_IDLE_CONNS", 10),
			MaxOpenConns:    getEnvInt("DB_MAX_OPEN_CONNS", 100),
			ConnMaxLifetime: getEnvInt("DB_CONN_MAX_LIFETIME", 60),
		},
		JWT: JWTConfig{
			SecretKey:       getEnv("JWT_SECRET_KEY", "your-secret-key"),
			ExpiryDuration:  time.Duration(getEnvInt("JWT_EXPIRY_HOURS", 24)) * time.Hour,
			RefreshDuration: time.Duration(getEnvInt("JWT_REFRESH_DAYS", 7)) * 24 * time.Hour,
			Issuer:          getEnv("JWT_ISSUER", "smooth-parking"),
		},
		Stripe: StripeConfig{
			SecretKey:      getEnv("STRIPE_SECRET_KEY", ""),
			PublishableKey: getEnv("STRIPE_PUBLISHABLE_KEY", ""),
			WebhookSecret:  getEnv("STRIPE_WEBHOOK_SECRET", ""),
		},
		Email: EmailConfig{
			Provider: getEnv("EMAIL_PROVIDER", "smtp"),
			Host:     getEnv("EMAIL_HOST", ""),
			Port:     getEnvInt("EMAIL_PORT", 587),
			Username: getEnv("EMAIL_USERNAME", ""),
			Password: getEnv("EMAIL_PASSWORD", ""),
			From:     getEnv("EMAIL_FROM", ""),
			FromName: getEnv("EMAIL_FROM_NAME", "Smooth Parking"),
			TLS:      getEnvBool("EMAIL_TLS", true),
		},
		AWS: AWSConfig{
			Region:          getEnv("AWS_REGION", "ap-northeast-1"),
			AccessKeyID:     getEnv("AWS_ACCESS_KEY_ID", ""),
			SecretAccessKey: getEnv("AWS_SECRET_ACCESS_KEY", ""),
			S3Bucket:        getEnv("AWS_S3_BUCKET", ""),
			S3Region:        getEnv("AWS_S3_REGION", "ap-northeast-1"),
		},
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}
