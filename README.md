# Smooth Parking Backend

A smart parking management system with automatic license plate detection, payment processing, and real-time parking management.

## Prerequisites

Before you begin, ensure you have the following installed:
- Go 1.21 or later
- PostgreSQL 15 or later
- `oapi-codegen` tool (for API code generation)
- Git

## Initial Setup

### 1. Install Required Tools

```bash
# Install oapi-codegen
go install github.com/oapi-codegen/oapi-codegen/v2/cmd/oapi-codegen@latest

# Verify installation
oapi-codegen --version
```

### 2. Database Setup

```sql
-- Connect to PostgreSQL and run:
CREATE DATABASE smooth_parking;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "postgis";
```

### 3. Environment Setup

Create a `.env` file in the project root:

```env
# Server Configuration
PORT=8080
ENV=development
DEBUG=true

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=smooth_parking
DB_SSL_MODE=disable

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRATION=24h
REFRESH_TOKEN_EXPIRATION=720h  # 30 days

# API Configuration
API_BASE_PATH=/api/v1
CORS_ALLOWED_ORIGINS=http://localhost:3000
```

Replace the values with your actual configuration.

### 4. Generate API Code

```bash
# Windows
.\scripts\generate.bat

# PowerShell
.\scripts\generate.ps1
```

## Running the Application

### 1. Install Dependencies

```bash
go mod download
go mod tidy
```

### 2. Run the Application

```bash
# Direct run
go run cmd/server/main.go

# Or using make (if available)
make run
```

The server will start at `http://localhost:8080` (or the port specified in your .env file).

## Development

### API Generation

The API is defined using OpenAPI 3.0 specification. When you modify the API spec, regenerate the code:

```bash
# Windows
.\scripts\generate.bat

# PowerShell
.\scripts\generate.ps1
```

### Watch Mode

To automatically regenerate code when the API spec changes:

```bash
# Windows PowerShell
.\scripts\generate.ps1 -Watch

# Make (if available)
make watch
```

### Project Structure

```
.
├── api/
│   ├── generated/     # Generated API code
│   └── spec/         # OpenAPI specifications
├── cmd/
│   └── server/       # Application entrypoint
├── internal/
│   ├── auth/         # Authentication logic
│   ├── config/       # Configuration management
│   ├── handler/      # HTTP handlers
│   ├── middleware/   # HTTP middleware
│   ├── model/        # Domain models
│   ├── repository/   # Data access layer
│   └── service/      # Business logic
├── pkg/              # Shared packages
├── scripts/          # Development scripts
└── .env              # Environment configuration
```

## API Documentation

Once the server is running, you can access the API documentation at:
- Swagger UI: `http://localhost:8080/swagger/`
- ReDoc: `http://localhost:8080/docs/`

## Testing

```bash
# Run all tests
go test ./...

# Run tests with coverage
go test -cover ./...
```

## Common Issues

1. **Database Connection Failed**
   - Verify PostgreSQL is running
   - Check database credentials in `.env`
   - Ensure database and extensions are created

2. **API Generation Errors**
   - Ensure `oapi-codegen` is installed
   - Verify OpenAPI spec syntax
   - Check file permissions

3. **Missing Environment Variables**
   - Copy `.env.example` to `.env`
   - Fill in all required values

## Contributing

1. Create a feature branch
2. Make your changes
3. Run tests
4. Submit a pull request

## License

This project is licensed under the Apache License 2.0 - see the LICENSE file for details.
