# Variables
APP_NAME := smooth-parking
BINARY_NAME := main
BUILD_DIR := bin
API_SPEC_DIR := api/spec
API_MAIN_SPEC := $(API_SPEC_DIR)/parking-api.yaml
GENERATED_DIR := api/generated
DOCKER_COMPOSE := docker-compose.yml

# Default target
.DEFAULT_GOAL := help

# Colors for output
GREEN := \033[0;32m
YELLOW := \033[0;33m
RED := \033[0;31m
NC := \033[0m # No Color

.PHONY: help
help: ## Show this help message
	@echo "$(GREEN)Available commands:$(NC)"
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m<target>\033[0m\n"} /^[a-zA-Z_0-9-]+:.*?##/ { printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) }' $(MAKEFILE_LIST)

##@ Development

.PHONY: install
install: ## Install dependencies and tools
	@echo "$(GREEN)Installing dependencies...$(NC)"
	go mod download
	go mod tidy
	@echo "$(GREEN)Installing oapi-codegen...$(NC)"
	go install github.com/oapi-codegen/oapi-codegen/v2/cmd/oapi-codegen@latest
	@echo "$(GREEN)Installing migrate tool...$(NC)"
	go install -tags 'postgres' github.com/golang-migrate/migrate/v4/cmd/migrate@latest

.PHONY: generate
generate: ## Generate API code from OpenAPI specification
	@echo "$(GREEN)Generating API code from OpenAPI spec...$(NC)"
	@if command -v pwsh > /dev/null; then \
		pwsh -File scripts/generate.ps1; \
	else \
		./scripts/generate.bat; \
	fi
	@echo "$(GREEN)Code generation completed!$(NC)"

.PHONY: build
build: generate ## Build the application
	@echo "$(GREEN)Building application...$(NC)"
	mkdir -p $(BUILD_DIR)
	go build -o $(BUILD_DIR)/$(BINARY_NAME) cmd/server/main.go
	@echo "$(GREEN)Build completed: $(BUILD_DIR)/$(BINARY_NAME)$(NC)"

.PHONY: run
run: generate ## Run the application locally
	@echo "$(GREEN)Starting application...$(NC)"
	go run cmd/server/main.go

.PHONY: dev
dev: generate ## Run the application in development mode with live reload
	@echo "$(GREEN)Starting development server...$(NC)"
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "$(YELLOW)Air not installed. Running without live reload...$(NC)"; \
		go run cmd/server/main.go; \
	fi

.PHONY: watch
watch: ## Watch for OpenAPI changes and regenerate code
	@echo "$(GREEN)Watching for OpenAPI spec changes...$(NC)"
	@if command -v pwsh > /dev/null; then \
		pwsh -File scripts/generate.ps1 -Watch; \
	elif command -v fswatch > /dev/null; then \
		fswatch -o $(API_SPEC_DIR)/*.yaml | xargs -n1 -I{} make generate; \
	elif command -v inotifywait > /dev/null; then \
		while inotifywait -e modify $(API_SPEC_DIR)/*.yaml; do make generate; done; \
	else \
		echo "$(RED)Install PowerShell, fswatch, or inotifywait for file watching$(NC)"; \
	fi

.PHONY: split-api
split-api: ## Split the OpenAPI spec into smaller files
	@echo "$(GREEN)Splitting OpenAPI specification...$(NC)"
	@mkdir -p $(API_SPEC_DIR)/components
	@mkdir -p $(API_SPEC_DIR)/paths
	@echo "$(YELLOW)Please run this manually or use a specialized tool for OpenAPI spec splitting$(NC)"

##@ Testing

.PHONY: test
test: ## Run tests
	@echo "$(GREEN)Running tests...$(NC)"
	go test -v ./...

.PHONY: test-coverage
test-coverage: ## Run tests with coverage
	@echo "$(GREEN)Running tests with coverage...$(NC)"
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "$(GREEN)Coverage report generated: coverage.html$(NC)"

.PHONY: test-race
test-race: ## Run tests with race detection
	@echo "$(GREEN)Running tests with race detection...$(NC)"
	go test -race -v ./...

.PHONY: benchmark
benchmark: ## Run benchmarks
	@echo "$(GREEN)Running benchmarks...$(NC)"
	go test -bench=. -benchmem ./...

##@ Code Quality

.PHONY: lint
lint: ## Run linter
	@echo "$(GREEN)Running linter...$(NC)"
	@if command -v golangci-lint > /dev/null; then \
		golangci-lint run; \
	else \
		echo "$(RED)Please install golangci-lint$(NC)"; \
	fi

.PHONY: fmt
fmt: ## Format code
	@echo "$(GREEN)Formatting code...$(NC)"
	go fmt ./...
	@if command -v goimports > /dev/null; then \
		goimports -w .; \
	fi

.PHONY: vet
vet: ## Run go vet
	@echo "$(GREEN)Running go vet...$(NC)"
	go vet ./...

.PHONY: tidy
tidy: ## Tidy go modules
	@echo "$(GREEN)Tidying go modules...$(NC)"
	go mod tidy

.PHONY: check
check: fmt vet lint ## Run all code quality checks

##@ Database

.PHONY: db-up
db-up: ## Start database with Docker
	@echo "$(GREEN)Starting database...$(NC)"
	docker-compose up -d postgres

.PHONY: db-down
db-down: ## Stop database
	@echo "$(GREEN)Stopping database...$(NC)"
	docker-compose down

.PHONY: db-migrate-up
db-migrate-up: ## Run database migrations up
	@echo "$(GREEN)Running database migrations up...$(NC)"
	migrate -path migrations -database "postgres://postgres:postgres@localhost:5432/smooth_parking?sslmode=disable" up

.PHONY: db-migrate-down
db-migrate-down: ## Run database migrations down
	@echo "$(GREEN)Running database migrations down...$(NC)"
	migrate -path migrations -database "postgres://postgres:postgres@localhost:5432/smooth_parking?sslmode=disable" down

.PHONY: db-migrate-create
db-migrate-create: ## Create new migration (usage: make db-migrate-create NAME=migration_name)
	@if [ -z "$(NAME)" ]; then \
		echo "$(RED)Please provide migration name: make db-migrate-create NAME=migration_name$(NC)"; \
		exit 1; \
	fi
	@echo "$(GREEN)Creating migration: $(NAME)$(NC)"
	migrate create -ext sql -dir migrations -seq $(NAME)

.PHONY: db-reset
db-reset: db-migrate-down db-migrate-up ## Reset database (down then up)

##@ Docker

.PHONY: docker-build
docker-build: ## Build Docker image
	@echo "$(GREEN)Building Docker image...$(NC)"
	docker build -t $(APP_NAME):latest .

.PHONY: docker-run
docker-run: docker-build ## Run application in Docker
	@echo "$(GREEN)Running application in Docker...$(NC)"
	docker run -p 8080:8080 --env-file .env $(APP_NAME):latest

.PHONY: docker-up
docker-up: ## Start all services with Docker Compose
	@echo "$(GREEN)Starting all services...$(NC)"
	docker-compose up -d

.PHONY: docker-down
docker-down: ## Stop all services
	@echo "$(GREEN)Stopping all services...$(NC)"
	docker-compose down

.PHONY: docker-logs
docker-logs: ## Show Docker logs
	docker-compose logs -f

##@ Documentation

.PHONY: docs
docs: ## Generate API documentation
	@echo "$(GREEN)Generating API documentation...$(NC)"
	@if command -v swagger > /dev/null; then \
		swagger generate spec -o api/spec/swagger.json; \
		echo "$(GREEN)Documentation generated: api/spec/swagger.json$(NC)"; \
	else \
		echo "$(YELLOW)Swagger not installed. Using OpenAPI spec directly.$(NC)"; \
	fi

.PHONY: docs-serve
docs-serve: ## Serve API documentation
	@echo "$(GREEN)Serving API documentation...$(NC)"
	@if command -v swagger > /dev/null; then \
		swagger serve api/spec/parking-api.yaml; \
	else \
		echo "$(YELLOW)Install swagger to serve docs$(NC)"; \
	fi

##@ Maintenance

.PHONY: clean
clean: ## Clean build artifacts
	@echo "$(GREEN)Cleaning build artifacts...$(NC)"
	rm -rf $(BUILD_DIR)
	rm -f coverage.out coverage.html
	rm -f main
	go clean -cache
	docker system prune -f

.PHONY: deps-update
deps-update: ## Update dependencies
	@echo "$(GREEN)Updating dependencies...$(NC)"
	go get -u ./...
	go mod tidy

.PHONY: security-check
security-check: ## Run security checks
	@echo "$(GREEN)Running security checks...$(NC)"
	@if command -v gosec > /dev/null; then \
		gosec ./...; \
	else \
		echo "$(YELLOW)Install gosec for security checks$(NC)"; \
	fi

##@ Complete Workflows

.PHONY: api-update
api-update: generate check test ## Complete workflow after updating OpenAPI spec
	@echo "$(GREEN)API update workflow completed!$(NC)"

.PHONY: deploy-prep
deploy-prep: clean generate check test build docker-build ## Prepare for deployment
	@echo "$(GREEN)Deployment preparation completed!$(NC)"

.PHONY: full-setup
full-setup: install db-up db-migrate-up generate ## Complete setup for new development environment
	@echo "$(GREEN)Full development setup completed!$(NC)"

.PHONY: quick-start
quick-start: generate run ## Quick start development server

# Special target for API spec changes
$(API_MAIN_SPEC): 
	@echo "$(YELLOW)OpenAPI spec changed, regenerating code...$(NC)"
	$(MAKE) generate 